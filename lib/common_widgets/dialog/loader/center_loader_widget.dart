import 'package:flutter/material.dart';
import 'package:khelnet/common_widgets/dialog/loader/spin_kit_fading_circle.dart';

import '../../../utils/constants/color_constant.dart';

class CenterLoader extends StatelessWidget {
  final double height, width;

  const CenterLoader({
    super.key,
    this.height = double.infinity,
    this.width = double.infinity,
  });

  @override
  SizedBox build(BuildContext context) => SizedBox(
        width: width,
        height: height,
        child: const Align(
          alignment: Alignment.center,
          child: CircularProgressIndicator.adaptive(),
        ),
      );
}

class BackgroundFadingSpinKitLoader extends StatelessWidget {
  const BackgroundFadingSpinKitLoader({super.key});

  @override
  Align build(BuildContext context) => Align(
        alignment: Alignment.center,
        child: Material(
          color: ColorConstant.white,
          elevation: 10,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          child: const Padding(
            padding: EdgeInsets.symmetric(vertical: 25, horizontal: 35),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SpinKitFadingCircle(color: ColorConstant.primaryColor),
                  ],
                ),
              ],
            ),
          ),
        ),
      );
}

class LoadingWidget extends StatelessWidget {
  const LoadingWidget({super.key});

  @override
  BackgroundFadingSpinKitLoader build(BuildContext context) =>
      const BackgroundFadingSpinKitLoader();
}

class SpinKitLoader extends StatelessWidget {
  final Color color;

  const SpinKitLoader({super.key, this.color = ColorConstant.primaryColor});

  @override
  Widget build(BuildContext context) => SpinKitFadingCircle(color: color);
}
