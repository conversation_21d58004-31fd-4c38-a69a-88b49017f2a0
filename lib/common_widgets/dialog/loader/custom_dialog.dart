import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:khelnet/common_widgets/buttons/app_elevated_button.dart';
import 'package:khelnet/common_widgets/custom_svg_picture.dart';
import 'package:khelnet/common_widgets/loader_helper.dart';
import 'package:khelnet/common_widgets/outlined_text_form_field.dart';
import 'package:khelnet/common_widgets/scrollbar_helper.dart';
import 'package:khelnet/global/constants/gradient_text.dart';
import 'package:khelnet/global/constants/size.dart';
import 'package:khelnet/utils/constants/app_asset.dart';
import 'package:khelnet/utils/constants/routes_constant.dart';
import 'package:khelnet/utils/extension/navigation_extension.dart';
import 'package:khelnet/utils/manager/storage_manager.dart';
import 'package:khelnet/utils/theme/typography.dart';
import 'package:numberpicker/numberpicker.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

import '../../../global/constants/app_constant.dart';
import '../../../modules/private_route/add_coach/controller/get_center_batch_bloc/get_center_batch_bloc.dart';
import '../../../modules/private_route/add_coach/controller/get_center_batch_bloc/get_center_batch_event.dart';
import '../../../modules/private_route/add_coach/controller/get_center_batch_bloc/get_center_batch_state.dart';
import '../../../modules/private_route/add_coach/model/get_batches_model.dart';
import '../../../modules/private_route/add_coach/model/get_center_model.dart';
import '../../../modules/private_route/fees/model/student_renew_model.dart';
import '../../../utils/constants/color_constant.dart';
import '../../multiple_selection_bottom_sheet.dart';
import '../../single_selection_bottom_sheet.dart';
import '../../toast/toast_utils.dart';

enum DialogType { loader, logout }

class CustomDialog {
  static final CustomDialog instance = CustomDialog._();

  factory CustomDialog() => instance;

  CustomDialog._();

  /// Method to Hide Dialog from current Context
  static void hideDialog(BuildContext context) => context.pop();

  /// Method to Hide Loader from current Context
  static void hideLoader(BuildContext context) => hideDialog(context);

  /// Method to Show Loader from current Context
  static void showLoader(BuildContext context) =>
      _showCustomDialog(context, DialogType.loader, "");

  // /// Method to show Logout Widget
  // static void showLogoutDialog(BuildContext context) => _showCustomDialog(
  //       context,
  //       DialogType.logout,
  //       "Session Expired",
  //       callMethodBeforePop: false,
  //       onYes: () async {
  //         showLoader(context);
  //         await appState.clearAllValues();
  //         await Future.delayed(const Duration(milliseconds: 300));
  //         context.pushNamedAndRemoveUntil(Routes.loginScreen);
  //       },
  //     );

  /// Method for handling of right dialog rendering
  static Future<void> _showCustomDialog(
    BuildContext context,
    DialogType dialogType,
    String message, {
    bool isDismissible = false,
  }) async =>
      await showDialog(
        context: context,
        barrierDismissible: isDismissible,
        builder: (context) =>
            PopScope(canPop: isDismissible, child: const LoaderHelper()),
      );

  static Future<void> showAlertBox(
    BuildContext context, {
    required String titleText,
    required String descriptionText,
    required String buttonText,
    required String image,
    required void Function() onTap,
    bool isDismissible = false,
  }) async {
    return showDialog(
        useSafeArea: false,
        context: context,
        builder: (context) => PopScope(
            canPop: isDismissible,
            child: Align(
              alignment: Alignment.center,
              child: Material(
                color: ColorConstant.white,
                elevation: 10,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                child: SizedBox(
                  height: MySize.getScaledSizeHeight(445),
                  width: MySize.getScaledSizeWidth(312),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Align(
                        alignment: Alignment.topRight,
                        child: GestureDetector(
                          onTap: () {
                            context.pop();
                          },
                          child: Padding(
                              padding: EdgeInsets.only(
                                  right: MySize.getScaledSizeWidth(18)),
                              child: const CustomSvgPicture(AppAsset.cross)),
                        ),
                      ),
                      SvgPicture.asset(image,
                          height: MySize.getScaledSizeHeight(122),
                          width: MySize.getScaledSizeHeight(114)),
                      Gap(MySize.getScaledSizeHeight(15)),
                      TypoGraphy.text(titleText, level: 4),
                      Gap(MySize.getScaledSizeHeight(15)),
                      Padding(
                          padding: EdgeInsets.all(MySize.size15 ?? 15),
                          child: TypoGraphy.text(descriptionText,
                              textAlign: TextAlign.center,
                              level: 2,
                              fontWeight: FontWeight.w500)),
                      Gap(MySize.getScaledSizeHeight(20)),
                      AppElevatedButton(
                          width: MySize.getScaledSizeWidth(256),
                          TypoGraphy.text(buttonText,
                              textAlign: TextAlign.center,
                              level: 2,
                              color: ColorConstant.white),
                          onPressed: onTap),
                      Gap(MySize.getScaledSizeHeight(10)),
                    ],
                  ),
                ),
              ),
            )));
  }

  static Future<void> congoAlert(BuildContext context,
      {required void Function() onTap}) async {
    String? academyName = AppConstant.academyName;

    if (context.mounted) {
      return showDialog(
        context: context,
        builder: (context) {
          return PopScope(
            canPop: false,
            child: Align(
              child: Material(
                elevation: 10,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                child: SizedBox(
                  height: MySize.getScaledSizeHeight(470),
                  width: MySize.getScaledSizeWidth(330),
                  child: Column(
                    children: [
                      Gap(MySize.getScaledSizeHeight(20)),
                      Padding(
                        padding: EdgeInsets.all(MySize.size8 ?? 8),
                        child: CustomSvgPicture(
                          AppAsset.congo,
                          width: MySize.getScaledSizeWidth(268),
                          height: MySize.getScaledSizeHeight(116),
                        ),
                      ),
                      Gap(MySize.getScaledSizeHeight(30)),
                      TypoGraphy.text("Congratulations !!", level: 5),
                      Gap(MySize.getScaledSizeHeight(22)),
                      TypoGraphy.text(
                        academyName,
                        level: 3,
                        color: ColorConstant.signUp,
                      ),
                      Gap(MySize.getScaledSizeHeight(20)),
                      TypoGraphy.text("Has Now Been Upgraded To",
                          level: 3, fontWeight: FontWeight.w600),
                      Gap(MySize.getScaledSizeHeight(5)),
                      TypoGraphy.text("Smart Academy",
                          color: ColorConstant.signUp, level: 3),
                      Gap(MySize.getScaledSizeHeight(44)),
                      AppElevatedButton(
                        width: MySize.getScaledSizeHeight(311),
                        TypoGraphy.text("Let's Begin",
                            color: ColorConstant.white, level: 3),
                        onPressed: () {
                          onTap();
                        },
                      )
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      );
    }
  }

  static Future<void> selectNumber(BuildContext context,
      {required void Function(int selectedNUmber) onTap,
      required bool isMonthly,
      required int preSelectedNumber}) async {
    int selectedNumber = preSelectedNumber == 0 ? 2 : preSelectedNumber;
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
                backgroundColor: ColorConstant.white,
                title: TypoGraphy.text('Select a Number', level: 2),
                content: SizedBox(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      NumberPicker(
                        value: selectedNumber,
                        zeroPad: true,
                        minValue: 2,
                        selectedTextStyle: TextStyle(
                            color: ColorConstant.secondaryColor,
                            fontSize: MySize.getScaledSizeHeight(25),
                            fontWeight: FontWeight.w700),
                        maxValue: isMonthly ? 5 : 8,
                        onChanged: (value) =>
                            setState(() => selectedNumber = value),
                      ),
                      Align(
                        alignment: Alignment.bottomRight,
                        child: TextButton(
                            onPressed: () {
                              onTap(selectedNumber);
                              context.pop();
                            },
                            child: TypoGraphy.text("Done",
                                level: 3, color: ColorConstant.signUp)),
                      )
                    ],
                  ),
                ));
          },
        );
      },
    );
  }

  static Future<void> installmentDialog(BuildContext context) async {
    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
          canPop: true,
          child: Align(
            child: Material(
              color: ColorConstant.white,
              elevation: 10,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15),
              ),
              child: SizedBox(
                width: MySize.getScaledSizeWidth(315),
                height: MySize.getScaledSizeHeight(400),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Gap(MySize.getScaledSizeHeight(15)),
                    const CustomSvgPicture(AppAsset.installments),
                    Gap(MySize.getScaledSizeHeight(28)),
                    TypoGraphy.text("Can’t Add Installments",
                        level: 3, fontWeight: FontWeight.w600),
                    Gap(MySize.getScaledSizeHeight(9)),
                    TypoGraphy.text(
                        "Installment can only be added In\n 6 Months or Yearly Plan",
                        level: 2,
                        color: ColorConstant.gray,
                        fontWeight: FontWeight.w500),
                    Gap(MySize.getScaledSizeHeight(30)),
                    Padding(
                      padding: EdgeInsets.only(
                          bottom: MySize.getScaledSizeHeight(15)),
                      child: AppElevatedButton(
                        height: MySize.getScaledSizeHeight(50),
                        width: MySize.getScaledSizeWidth(249),
                        TypoGraphy.text("Okay",
                            level: 3, color: ColorConstant.white),
                        onPressed: () {
                          context.pop();
                        },
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  static Future<void> deleteAlert(
    BuildContext context, {
    required int index,
    bool isForFees = false,
    bool isForEnquiry = false,
    required String title,
    required String description,
    required Function(int index) onTap,
  }) async {
    return showDialog(
      context: context,
      builder: (context) {
        return PopScope(
          canPop: false,
          child: Align(
            child: Material(
              color: ColorConstant.white,
              elevation: 10,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15),
              ),
              child: SizedBox(
                height: MySize.getScaledSizeHeight(445),
                width: MySize.getScaledSizeWidth(318),
                child: Column(
                  children: [
                    Gap(MySize.getScaledSizeHeight(30)),
                    Padding(
                      padding: EdgeInsets.all(MySize.size8 ?? 8),
                      child: CustomSvgPicture(
                        AppAsset.deleteSvg,
                        width: MySize.getScaledSizeWidth(268),
                        height: MySize.getScaledSizeHeight(116),
                      ),
                    ),
                    Gap(MySize.getScaledSizeHeight(10)),
                    TypoGraphy.text(title,
                        fontWeight: FontWeight.w600, level: 5),
                    Gap(MySize.getScaledSizeHeight(30)),
                    TypoGraphy.text(
                      fontWeight: FontWeight.w500,
                      fontSize: MySize.getScaledSizeHeight(12),
                      description,
                      color: ColorConstant.gray,
                    ),
                    Gap(MySize.getScaledSizeHeight(20)),
                    GestureDetector(
                      onTap: () {
                        onTap(index);
                      },
                      child: Container(
                          width: MySize.getScaledSizeHeight(248),
                          height: MySize.getScaledSizeHeight(47),
                          decoration: BoxDecoration(
                            border:
                                Border.all(color: ColorConstant.primaryColor),
                            color: ColorConstant.white,
                            borderRadius:
                                const BorderRadius.all(Radius.circular(25)),
                          ),
                          child: Center(
                            child: TypoGraphy.text(
                                isForEnquiry
                                    ? "Close Enquiry"
                                    : isForFees
                                        ? "Confirm"
                                        : "Delete",
                                color: ColorConstant.primaryColor,
                                level: 3),
                          )),
                    ),
                    Gap(MySize.getScaledSizeHeight(15)),
                    GestureDetector(
                      onTap: () => context.pop(),
                      child: Container(
                          width: MySize.getScaledSizeHeight(248),
                          height: MySize.getScaledSizeHeight(47),
                          decoration: BoxDecoration(
                            border:
                                Border.all(color: ColorConstant.primaryColor),
                            gradient: const LinearGradient(colors: [
                              ColorConstant.gradient1,
                              ColorConstant.gradient2,
                            ]),
                            borderRadius:
                                const BorderRadius.all(Radius.circular(25)),
                          ),
                          child: Center(
                            child: TypoGraphy.text("Cancel",
                                color: ColorConstant.white, level: 3),
                          )),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  static Future<void> multiplePayments(
    BuildContext context, {
    required Function() onTap,
  }) async {
    return showDialog(
      context: context,
      builder: (context) {
        return PopScope(
          canPop: false,
          child: Align(
            child: Material(
              color: ColorConstant.white,
              elevation: 10,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15),
              ),
              child: SizedBox(
                height: MySize.getScaledSizeHeight(445),
                width: MySize.getScaledSizeWidth(318),
                child: Column(
                  children: [
                    Gap(MySize.getScaledSizeHeight(30)),
                    Padding(
                      padding: EdgeInsets.all(MySize.size8 ?? 8),
                      child: CustomSvgPicture(
                        AppAsset.multiplePayments,
                        width: MySize.getScaledSizeWidth(287),
                        height: MySize.getScaledSizeHeight(237),
                      ),
                    ),
                    Gap(MySize.getScaledSizeHeight(10)),
                    GestureDetector(
                      onTap: () {
                        onTap();
                      },
                      child: Container(
                          width: MySize.getScaledSizeHeight(240),
                          height: MySize.getScaledSizeHeight(47),
                          decoration: BoxDecoration(
                            border:
                                Border.all(color: ColorConstant.primaryColor),
                            color: ColorConstant.white,
                            borderRadius:
                                const BorderRadius.all(Radius.circular(25)),
                          ),
                          child: Center(
                            child: TypoGraphy.text("Confirm",
                                color: ColorConstant.primaryColor, level: 3),
                          )),
                    ),
                    Gap(MySize.getScaledSizeHeight(15)),
                    GestureDetector(
                      onTap: () => context.pop(),
                      child: Container(
                          width: MySize.getScaledSizeHeight(240),
                          height: MySize.getScaledSizeHeight(47),
                          decoration: BoxDecoration(
                            border:
                                Border.all(color: ColorConstant.primaryColor),
                            gradient: const LinearGradient(colors: [
                              ColorConstant.gradient1,
                              ColorConstant.gradient2,
                            ]),
                            borderRadius:
                                const BorderRadius.all(Radius.circular(25)),
                          ),
                          child: Center(
                            child: TypoGraphy.text("Cancel",
                                color: ColorConstant.white, level: 3),
                          )),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  static showStartTimer(
    BuildContext context, {
    bool isDismissible = false,
    DateTime? initialStartTime,
    required String startTime,
    required void Function(String time, DateTime initialTime) onTap,
  }) {
    DateTime initialTime = DateTime.now();
    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
          canPop: isDismissible,
          child: Align(
            child: Material(
              color: ColorConstant.white,
              elevation: 10,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15),
              ),
              child: SizedBox(
                height: MySize.getScaledSizeHeight(330),
                width: MySize.getScaledSizeWidth(300),
                child: Column(
                  children: [
                    Gap(MySize.getScaledSizeHeight(10)),
                    TypoGraphy.text("Select Start Time", level: 3),
                    SizedBox(
                      height: MySize.getScaledSizeHeight(250),
                      child: CupertinoDatePicker(
                        initialDateTime: initialStartTime ?? DateTime.now(),
                        use24hFormat: false,
                        showDayOfWeek: false,
                        itemExtent: 55,
                        mode: CupertinoDatePickerMode.time,
                        onDateTimeChanged: (DateTime value) {
                          String formattedTime =
                              DateFormat('hh:mm a').format(value);
                          startTime = formattedTime;
                          initialTime = value;
                          log(formattedTime);
                        },
                      ),
                    ),
                    Padding(
                      padding:
                          EdgeInsets.only(right: MySize.getScaledSizeWidth(18)),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          GradientText(
                            gradient: const LinearGradient(colors: [
                              ColorConstant.white,
                              ColorConstant.primaryColor,
                            ]),
                            child: GestureDetector(
                              onTap: () => context.pop(),
                              child: TypoGraphy.text(
                                "Cancel",
                                color: ColorConstant.primaryColor,
                                fontSize: MySize.getScaledSizeHeight(20),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          Gap(MySize.getScaledSizeWidth(20)),
                          Padding(
                            padding: EdgeInsets.only(
                                right: MySize.getScaledSizeWidth(8)),
                            child: GradientText(
                              gradient: const LinearGradient(colors: [
                                ColorConstant.white,
                                ColorConstant.primaryColor,
                              ]),
                              child: GestureDetector(
                                onTap: () {
                                  onTap(startTime, initialTime);
                                  context.pop();
                                },
                                child: TypoGraphy.text(
                                  "Ok",
                                  color: ColorConstant.primaryColor,
                                  fontSize: MySize.getScaledSizeHeight(20),
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  static showEndTimer(
    BuildContext context, {
    bool isDismissible = false,
    DateTime? initialEndTime,
    required String endTime,
    required void Function(String time, DateTime initialTime) onTap,
  }) {
    DateTime initialSelectedEndTime = DateTime.now();
    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
          canPop: isDismissible,
          child: Align(
            child: Material(
              color: ColorConstant.white,
              elevation: 10,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15),
              ),
              child: SizedBox(
                height: MySize.getScaledSizeHeight(330),
                width: MySize.getScaledSizeWidth(300),
                child: Column(
                  children: [
                    Gap(MySize.getScaledSizeHeight(10)),
                    TypoGraphy.text("Select End Time", level: 3),
                    SizedBox(
                      height: MySize.getScaledSizeHeight(250),
                      child: CupertinoDatePicker(
                        initialDateTime: initialEndTime,
                        use24hFormat: false,
                        itemExtent: 55,
                        showDayOfWeek: false,
                        mode: CupertinoDatePickerMode.time,
                        onDateTimeChanged: (DateTime value) {
                          initialSelectedEndTime = value;
                          String formattedTime =
                              DateFormat('hh:mm a').format(value);
                          endTime = formattedTime;
                          log(formattedTime);
                        },
                      ),
                    ),
                    Padding(
                      padding:
                          EdgeInsets.only(right: MySize.getScaledSizeWidth(18)),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          GradientText(
                            gradient: const LinearGradient(colors: [
                              ColorConstant.white,
                              ColorConstant.primaryColor,
                            ]),
                            child: GestureDetector(
                              onTap: () => context.pop(),
                              child: TypoGraphy.text(
                                "Cancel",
                                color: ColorConstant.primaryColor,
                                fontSize: MySize.getScaledSizeHeight(20),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          Gap(MySize.getScaledSizeWidth(20)),
                          Padding(
                            padding: EdgeInsets.only(right: MySize.size8 ?? 8),
                            child: GradientText(
                              gradient: const LinearGradient(colors: [
                                ColorConstant.white,
                                ColorConstant.primaryColor,
                              ]),
                              child: GestureDetector(
                                onTap: () {
                                  onTap(endTime, initialSelectedEndTime);
                                  context.pop();
                                },
                                child: TypoGraphy.text(
                                  "Ok",
                                  color: ColorConstant.primaryColor,
                                  fontSize: MySize.getScaledSizeHeight(20),
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  static showDatePickerDialog(
    BuildContext context, {
    required Function(DateTime selectedDate) onTap,
    required DateTime initialDate,
    required DateTime selectedDate,
  }) {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) => Container(
        height: 216,
        padding: EdgeInsets.only(top: MySize.getScaledSizeHeight(6)),
        margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        color: CupertinoColors.systemBackground.resolveFrom(context),
        child: SafeArea(
          top: false,
          child: Column(
            children: [
              Expanded(
                child: CupertinoDatePicker(
                  initialDateTime: initialDate,
                  mode: CupertinoDatePickerMode.date,
                  use24hFormat: false,
                  dateOrder: DatePickerDateOrder.dmy,
                  onDateTimeChanged: (DateTime newDate) {
                    selectedDate = newDate;
                  },
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () {
                      context.pop();
                    },
                    child: TypoGraphy.text(
                      "Cancel",
                      color: ColorConstant.primaryColor,
                      fontSize: MySize.getScaledSizeHeight(20),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      onTap(selectedDate);
                      context.pop();
                    },
                    child: TypoGraphy.text(
                      "Ok",
                      color: ColorConstant.primaryColor,
                      fontSize: MySize.getScaledSizeHeight(20),
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  static showSpecificDialog(BuildContext context,
      {required String title,
      required String btnText,
      required String image,
      required String routeName}) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: ColorConstant.white,
          content: SizedBox(
            height: MySize.getScaledSizeHeight(362),
            width: MySize.getScaledSizeWidth(274),
            child: Column(
              children: [
                Gap(MySize.getScaledSizeHeight(20)),
                CustomSvgPicture(image),
                Gap(MySize.getScaledSizeHeight(20)),
                TypoGraphy.text(title, level: 1),
                Gap(MySize.getScaledSizeHeight(20)),
                AppElevatedButton(
                  width: MySize.getScaledSizeWidth(213),
                  height: MySize.getScaledSizeHeight(60),
                  TypoGraphy.text(btnText,
                      level: 1, color: ColorConstant.white),
                  onPressed: () {
                    context.pushNamed(routeName);
                  },
                )
              ],
            ),
          ),
        );
      },
    );
  }

  static showUpcomingStudentDialog(BuildContext context,
      {required String name,
      required String batchName,
      required int id,
      required bool isForUpcoming,
      void Function()? activeTap,
      void Function()? presentTap,
      required File profileImage,
      required String batchTime}) {
    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
            child: Align(
          child: Material(
            color: ColorConstant.white,
            elevation: 10,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            child: SizedBox(
              height: MySize.getScaledSizeHeight(404),
              width: MySize.getScaledSizeWidth(322),
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.only(
                        right: MySize.getScaledSizeWidth(12),
                        top: MySize.getScaledSizeHeight(12)),
                    child: Align(
                        alignment: Alignment.topRight,
                        child: GestureDetector(
                          onTap: () {
                            context.pop();
                          },
                          child: CustomSvgPicture(
                            AppAsset.crossIcon,
                            height: MySize.getScaledSizeHeight(20),
                          ),
                        )),
                  ),
                  CircleAvatar(
                      radius: 35,
                      backgroundColor: profileImage.path != AppAsset.studentIcon
                          ? ColorConstant.transparent
                          : ColorConstant.studentImage,
                      child: ClipOval(
                        clipBehavior: Clip.hardEdge,
                        child: profileImage.path != AppAsset.studentIcon
                            ? Image.network(
                                profileImage.path,
                                fit: BoxFit.cover,
                                height: MySize.getScaledSizeHeight(70),
                                width: MySize.getScaledSizeWidth(70),
                              )
                            : Padding(
                                padding: EdgeInsets.only(
                                    top: MySize.getScaledSizeHeight(8)),
                                child: CustomSvgPicture(
                                  AppAsset.studentIcon,
                                  height: MySize.getScaledSizeHeight(42),
                                ),
                              ),
                      )),
                  Gap(MySize.getScaledSizeHeight(8)),
                  TypoGraphy.text(name, level: 3, fontWeight: FontWeight.w500),
                  Gap(MySize.getScaledSizeHeight(3)),
                  TypoGraphy.text(batchName,
                      fontSize: MySize.getScaledSizeHeight(13),
                      fontWeight: FontWeight.w500),
                  Gap(MySize.getScaledSizeHeight(5)),
                  TypoGraphy.text(batchTime,
                      fontSize: MySize.getScaledSizeHeight(13),
                      fontWeight: FontWeight.w500),
                  Gap(MySize.getScaledSizeHeight(15)),
                  Container(
                    height: MySize.getScaledSizeHeight(27),
                    width: MySize.getScaledSizeWidth(170),
                    decoration: BoxDecoration(
                        borderRadius:
                            const BorderRadius.all(Radius.circular(34)),
                        color: Color(isForUpcoming ? 0XFFE9D48B : 0XFFFFCFCF)),
                    child: Center(
                      child: TypoGraphy.text(
                          isForUpcoming ? "Upcoming" : "Inactive",
                          fontSize: MySize.getScaledSizeHeight(14),
                          color: Color(isForUpcoming ? 0XFF7A4609 : 0XFF8F0000),
                          fontWeight: FontWeight.w500),
                    ),
                  ),
                  Gap(MySize.getScaledSizeHeight(34)),
                  GestureDetector(
                    onTap: isForUpcoming ? presentTap : activeTap,
                    child: Container(
                      width: MySize.getScaledSizeWidth(281),
                      height: MySize.getScaledSizeHeight(48),
                      decoration: const BoxDecoration(
                          color: ColorConstant.textFieldColor,
                          borderRadius: BorderRadius.all(Radius.circular(17))),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CustomSvgPicture(isForUpcoming
                              ? AppAsset.markAsPresent
                              : AppAsset.activeIcon),
                          Gap(MySize.getScaledSizeWidth(10)),
                          TypoGraphy.text(
                              isForUpcoming ? "Mark As Present" : "Mark Active",
                              level: 1,
                              fontWeight: FontWeight.w500)
                        ],
                      ),
                    ),
                  ),
                  Gap(MySize.getScaledSizeHeight(15)),
                  GestureDetector(
                    onTap: () {
                      context.pushNamed(RouteConstant.studentProfile,
                          args: {"studentId": id});
                    },
                    child: Container(
                      width: MySize.getScaledSizeWidth(281),
                      height: MySize.getScaledSizeHeight(48),
                      decoration: const BoxDecoration(
                          color: ColorConstant.textFieldColor,
                          borderRadius: BorderRadius.all(Radius.circular(17))),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const CustomSvgPicture(AppAsset.viewProfile),
                          Gap(MySize.getScaledSizeWidth(10)),
                          TypoGraphy.text("View Profile",
                              level: 1, fontWeight: FontWeight.w500)
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ));
      },
    );
  }

  static showAnnounceClassDialog(BuildContext context,
      {required TextEditingController reasonController,
      required TextEditingController descriptionController}) {
    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
            child: Align(
          child: Material(
            color: ColorConstant.white,
            elevation: 10,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            child: SizedBox(
              height: MySize.getScaledSizeHeight(545),
              width: MySize.getScaledSizeWidth(369),
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.only(
                        right: MySize.getScaledSizeWidth(12),
                        top: MySize.getScaledSizeHeight(12)),
                    child: Align(
                        alignment: Alignment.topRight,
                        child: GestureDetector(
                          onTap: () {
                            context.pop();
                          },
                          child: CustomSvgPicture(
                            AppAsset.crossIcon,
                            height: MySize.getScaledSizeHeight(20),
                          ),
                        )),
                  ),
                  const CustomSvgPicture(AppAsset.announceClass),
                  Gap(MySize.getScaledSizeHeight(15)),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      TypoGraphy.text("23",
                          fontWeight: FontWeight.w600,
                          fontSize: MySize.getScaledSizeHeight(26)),
                      Padding(
                        padding:
                            EdgeInsets.only(top: MySize.getScaledSizeHeight(7)),
                        child: TypoGraphy.text("April",
                            level: 2, fontWeight: FontWeight.w600),
                      ),
                      Gap(MySize.getScaledSizeWidth(12)),
                      Padding(
                        padding:
                            EdgeInsets.only(top: MySize.getScaledSizeHeight(7)),
                        child: CustomSvgPicture(
                          AppAsset.calenderSvg,
                          height: MySize.getScaledSizeHeight(15),
                        ),
                      ),
                    ],
                  ),
                  Gap(MySize.getScaledSizeHeight(10)),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      TypoGraphy.text("Every center and batch",
                          level: 2, fontWeight: FontWeight.w500),
                      Gap(MySize.getScaledSizeWidth(10)),
                      const Icon(Icons.keyboard_arrow_down_rounded)
                    ],
                  ),
                  Gap(MySize.getScaledSizeHeight(15)),
                  OutlinedTextFormField(
                    controller: reasonController,
                    hintText: "Reason",
                  ),
                  OutlinedTextFormField(
                    controller: descriptionController,
                    hintText: "Description (optional)",
                  ),
                  Gap(MySize.getScaledSizeHeight(30)),
                  AppElevatedButton(
                    height: MySize.getScaledSizeHeight(57),
                    width: MySize.getScaledSizeWidth(258),
                    TypoGraphy.text("Announce",
                        level: 3, color: ColorConstant.white),
                    onPressed: () {},
                  )
                ],
              ),
            ),
          ),
        ));
      },
    );
  }

  static showDownloadReportDialog(
    BuildContext context, {
    required String name,
    required String batchName,
    bool isForStudent = true,
    bool isForAssessment = false,
    required void Function() onTap,
    required TextEditingController startDateController,
    required TextEditingController endDateController,
    required ValueNotifier<bool> is7DaySelected,
    required ValueNotifier<bool> isCurrentMonthSelected,
    required ValueNotifier<bool> isLastMonthSelected,
    required File profileImage,
  }) {
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return PopScope(
                child: Align(
              child: Material(
                color: ColorConstant.white,
                elevation: 10,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                child: SizedBox(
                  height:
                      MySize.getScaledSizeHeight(isForAssessment ? 400 : 470),
                  width: MySize.getScaledSizeWidth(334),
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.only(
                            right: MySize.getScaledSizeWidth(12),
                            top: MySize.getScaledSizeHeight(12)),
                        child: Align(
                            alignment: Alignment.topRight,
                            child: GestureDetector(
                              onTap: () {
                                context.pop();
                              },
                              child: CustomSvgPicture(
                                AppAsset.crossIcon,
                                height: MySize.getScaledSizeHeight(20),
                              ),
                            )),
                      ),
                      Padding(
                        padding: EdgeInsets.all(MySize.size10 ?? 10),
                        child: CircleAvatar(
                            radius: 35,
                            backgroundColor: profileImage.path !=
                                    (isForStudent
                                        ? AppAsset.studentIcon
                                        : AppAsset.coachIcon)
                                ? ColorConstant.transparent
                                : isForStudent
                                    ? ColorConstant.studentImage
                                    : ColorConstant.coachBackground,
                            child: ClipOval(
                              clipBehavior: Clip.hardEdge,
                              child: profileImage.path !=
                                      (isForStudent
                                          ? AppAsset.studentIcon
                                          : AppAsset.coachIcon)
                                  ? Image.network(
                                      profileImage.path,
                                      fit: BoxFit.cover,
                                      height: MySize.getScaledSizeHeight(70),
                                      width: MySize.getScaledSizeWidth(70),
                                    )
                                  : Padding(
                                      padding: EdgeInsets.only(
                                          top: MySize.getScaledSizeHeight(8)),
                                      child: CustomSvgPicture(
                                        isForStudent
                                            ? AppAsset.studentIcon
                                            : AppAsset.coachIcon,
                                        height: MySize.getScaledSizeHeight(75),
                                      ),
                                    ),
                            )),
                      ),
                      if (!isForAssessment)
                        TypoGraphy.text(name,
                            level: 3, fontWeight: FontWeight.w500),
                      if (!isForAssessment) Gap(MySize.getScaledSizeHeight(3)),
                      TypoGraphy.text(batchName,
                          fontSize: MySize.getScaledSizeHeight(13),
                          fontWeight: FontWeight.w500),
                      if (!isForAssessment) Gap(MySize.getScaledSizeHeight(24)),
                      Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: MySize.getScaledSizeWidth(8)),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Expanded(
                              child: OutlinedTextFormField(
                                horizontalPadding: 0,
                                fillColor: ColorConstant.textFieldColor,
                                prefix: const CustomSvgPicture(
                                    AppAsset.customCalender),
                                onTap: (context) async {
                                  DateTime initialDate = DateTime.now();
                                  DateTime? pickedDate = await showDatePicker(
                                    builder: (context, child) {
                                      return Theme(
                                          data: Theme.of(context).copyWith(
                                              colorScheme:
                                                  const ColorScheme.light(
                                                primary: ColorConstant.signUp,
                                                onPrimary: Colors.white,
                                                onSurface: Colors
                                                    .black, // Month days , years
                                              ),
                                              textButtonTheme:
                                                  TextButtonThemeData(
                                                style: TextButton.styleFrom(
                                                  foregroundColor: ColorConstant
                                                      .signUp, // ok , cancel    buttons
                                                ),
                                              ),
                                              dialogBackgroundColor:
                                                  ColorConstant.white),
                                          child: child!);
                                    },
                                    context: context,
                                    initialDate: startDateController
                                            .text.isEmpty
                                        ? initialDate
                                        : DateFormat('dd-MM-yyyy')
                                            .parse(startDateController.text),
                                    firstDate: DateTime(1800),
                                    lastDate: DateTime.now(),
                                  );
                                  if (pickedDate != null) {
                                    setState(() {
                                      startDateController.text =
                                          DateFormat('dd-MM-yyyy')
                                              .format(pickedDate);
                                      initialDate = pickedDate;
                                    });
                                  }
                                },
                                controller: startDateController,
                                hintText: "Start Date",
                              ),
                            ),
                            Gap(MySize.getScaledSizeWidth(5)),
                            Expanded(
                              child: OutlinedTextFormField(
                                horizontalPadding: 0,
                                fillColor: ColorConstant.textFieldColor,
                                prefix: const CustomSvgPicture(
                                    AppAsset.customCalender),
                                onTap: (context) async {
                                  DateTime initialDate = DateTime.now();
                                  DateTime? pickedDate = await showDatePicker(
                                    builder: (context, child) {
                                      return Theme(
                                          data: Theme.of(context).copyWith(
                                              colorScheme:
                                                  const ColorScheme.light(
                                                primary: ColorConstant.signUp,
                                                onPrimary: Colors.white,
                                                onSurface: Colors
                                                    .black, // Month days , years
                                              ),
                                              textButtonTheme:
                                                  TextButtonThemeData(
                                                style: TextButton.styleFrom(
                                                  foregroundColor: ColorConstant
                                                      .signUp, // ok , cancel    buttons
                                                ),
                                              ),
                                              dialogBackgroundColor:
                                                  ColorConstant.white),
                                          child: child!);
                                    },
                                    context: context,
                                    initialDate: endDateController.text.isEmpty
                                        ? initialDate
                                        : DateFormat('dd-MM-yyyy')
                                            .parse(endDateController.text),
                                    firstDate: DateTime(1800),
                                    lastDate: startDateController
                                            .text.isNotEmpty
                                        ? !initialDate.isAfter(
                                                DateFormat('dd-MM-yyyy').parse(
                                                    startDateController.text))
                                            ? startDateController.text.isEmpty
                                                ? DateTime.now()
                                                : DateFormat('dd-MM-yyyy')
                                                    .parse(startDateController
                                                        .text)
                                            : DateTime.now()
                                        : DateTime.now(),
                                  );
                                  if (pickedDate != null) {
                                    setState(() {
                                      endDateController.text =
                                          DateFormat('dd-MM-yyyy')
                                              .format(pickedDate);
                                      initialDate = pickedDate;
                                    });
                                  }
                                },
                                controller: endDateController,
                                hintText: "End Date",
                              ),
                            ),
                          ],
                        ),
                      ),
                      Gap(MySize.getScaledSizeHeight(5)),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ValueListenableBuilder(
                            valueListenable: is7DaySelected,
                            builder: (context, value, child) => GestureDetector(
                              onTap: () {
                                is7DaySelected.value = !is7DaySelected.value;
                                isCurrentMonthSelected.value = false;
                                isLastMonthSelected.value = false;
                                endDateController.text =
                                    DateFormat("dd-MM-yyyy")
                                        .format(DateTime.now());
                                startDateController.text =
                                    DateFormat("dd-MM-yyyy").format(
                                        DateTime.now()
                                            .subtract(const Duration(days: 7)));
                                if (!is7DaySelected.value) {
                                  startDateController.clear();
                                  endDateController.clear();
                                }
                              },
                              child: Container(
                                height: MySize.getScaledSizeHeight(37),
                                width: MySize.getScaledSizeWidth(98),
                                decoration: BoxDecoration(
                                    gradient: LinearGradient(colors: [
                                      is7DaySelected.value
                                          ? ColorConstant.gradient1
                                          : ColorConstant.textFieldColor,
                                      is7DaySelected.value
                                          ? ColorConstant.gradient2
                                          : ColorConstant.textFieldColor,
                                    ]),
                                    color: is7DaySelected.value
                                        ? ColorConstant.primaryColor
                                        : ColorConstant.textFieldColor,
                                    borderRadius: const BorderRadius.all(
                                        Radius.circular(13))),
                                child: Center(
                                    child: Padding(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: MySize.getScaledSizeWidth(13),
                                      vertical:
                                          MySize.getScaledSizeHeight(5.95)),
                                  child: TypoGraphy.text("Last 7 Days",
                                      fontWeight: FontWeight.w500,
                                      color: is7DaySelected.value
                                          ? ColorConstant.white
                                          : ColorConstant.black,
                                      fontSize: MySize.getScaledSizeHeight(12)),
                                )),
                              ),
                            ),
                          ),
                          Gap(MySize.getScaledSizeWidth(14)),
                          ValueListenableBuilder(
                            valueListenable: isCurrentMonthSelected,
                            builder: (context, value, child) => GestureDetector(
                              onTap: () {
                                isCurrentMonthSelected.value =
                                    !isCurrentMonthSelected.value;
                                is7DaySelected.value = false;
                                isLastMonthSelected.value = false;
                                endDateController.text =
                                    DateFormat("dd-MM-yyyy")
                                        .format(DateTime.now());
                                startDateController.text =
                                    DateFormat("dd-MM-yyyy").format(DateTime(
                                        DateTime.now().year,
                                        DateTime.now().month,
                                        1));
                                if (!isCurrentMonthSelected.value) {
                                  startDateController.clear();
                                  endDateController.clear();
                                }
                              },
                              child: Container(
                                height: MySize.getScaledSizeHeight(37),
                                width: MySize.getScaledSizeWidth(104),
                                decoration: BoxDecoration(
                                    gradient: LinearGradient(colors: [
                                      isCurrentMonthSelected.value
                                          ? ColorConstant.gradient1
                                          : ColorConstant.textFieldColor,
                                      isCurrentMonthSelected.value
                                          ? ColorConstant.gradient2
                                          : ColorConstant.textFieldColor,
                                    ]),
                                    color: isCurrentMonthSelected.value
                                        ? ColorConstant.primaryColor
                                        : ColorConstant.textFieldColor,
                                    borderRadius: const BorderRadius.all(
                                        Radius.circular(13))),
                                child: Center(
                                    child: TypoGraphy.text("Current Month",
                                        fontWeight: FontWeight.w500,
                                        color: isCurrentMonthSelected.value
                                            ? ColorConstant.white
                                            : ColorConstant.black,
                                        fontSize:
                                            MySize.getScaledSizeHeight(12))),
                              ),
                            ),
                          ),
                        ],
                      ),
                      Gap(MySize.getScaledSizeHeight(10)),
                      ValueListenableBuilder(
                        valueListenable: isLastMonthSelected,
                        builder: (context, value, child) => GestureDetector(
                          onTap: () {
                            isLastMonthSelected.value =
                                !isLastMonthSelected.value;
                            is7DaySelected.value = false;
                            isCurrentMonthSelected.value = false;
                            DateTime now = DateTime.now();
                            DateTime firstDayLastMonth =
                                DateTime(now.year, now.month - 1, 1);
                            DateTime lastDayLastMonth =
                                DateTime(now.year, now.month, 0);
                            startDateController.text = DateFormat('dd-MM-yyyy')
                                .format(firstDayLastMonth);
                            endDateController.text = DateFormat('dd-MM-yyyy')
                                .format(lastDayLastMonth);
                            if (!isLastMonthSelected.value) {
                              startDateController.clear();
                              endDateController.clear();
                            }
                          },
                          child: Container(
                            height: MySize.getScaledSizeHeight(37),
                            width: MySize.getScaledSizeWidth(98),
                            decoration: BoxDecoration(
                                gradient: LinearGradient(colors: [
                                  isLastMonthSelected.value
                                      ? ColorConstant.gradient1
                                      : ColorConstant.textFieldColor,
                                  isLastMonthSelected.value
                                      ? ColorConstant.gradient2
                                      : ColorConstant.textFieldColor,
                                ]),
                                color: isLastMonthSelected.value
                                    ? ColorConstant.primaryColor
                                    : ColorConstant.textFieldColor,
                                borderRadius: const BorderRadius.all(
                                    Radius.circular(13))),
                            child: Center(
                                child: TypoGraphy.text("Last Month",
                                    fontWeight: FontWeight.w500,
                                    color: isLastMonthSelected.value
                                        ? ColorConstant.white
                                        : ColorConstant.black,
                                    fontSize: MySize.getScaledSizeHeight(12))),
                          ),
                        ),
                      ),
                      Gap(MySize.getScaledSizeHeight(20)),
                      AppElevatedButton(
                        width: MySize.getScaledSizeWidth(248),
                        TypoGraphy.text(
                            isForAssessment
                                ? "Select Range"
                                : "Download Report",
                            level: 2,
                            fontWeight: FontWeight.w600,
                            color: ColorConstant.white),
                        onPressed: onTap,
                      )
                    ],
                  ),
                ),
              ),
            ));
          },
        );
      },
    );
  }

  static showExpenseReport(
    BuildContext context, {
    required void Function(List<int> centerids) onTap,
    required TextEditingController startDateController,
    required List<Centers> assignCenters,
    // required List<Centers> selectedCenters,
    required List<ValueNotifier<bool>> isCenterSelected,
    required TextEditingController centerController,
    required TextEditingController endDateController,
    required ValueNotifier<bool> is7DaySelected,
    required ValueNotifier<bool> isCurrentMonthSelected,
    required ValueNotifier<bool> isLastMonthSelected,
  }) {
    context.read<GetCenterBatchBloc>().add(GetCenters());

    List<Centers> selectedCenters = [];
    List<int> selectedCenterIDs = [];
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return PopScope(
                canPop: false,
                child: Align(
                  child: Material(
                    color: ColorConstant.white,
                    elevation: 10,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: SizedBox(
                      height: MySize.getScaledSizeHeight(350),
                      width: MySize.getScaledSizeWidth(345),
                      child: Column(
                        children: [
                          Padding(
                            padding: EdgeInsets.only(
                                right: MySize.getScaledSizeWidth(12),
                                top: MySize.getScaledSizeHeight(12)),
                            child: Row(
                              children: [
                                Expanded(
                                    child: Padding(
                                  padding:
                                      EdgeInsets.only(left: MySize.size8 ?? 8),
                                  child: TypoGraphy.text("Expense Report",
                                      level: 2),
                                )),
                                Align(
                                    alignment: Alignment.topRight,
                                    child: GestureDetector(
                                      onTap: () {
                                        context.pop();
                                        startDateController.clear();
                                        endDateController.clear();
                                        centerController.clear();
                                        is7DaySelected.value = false;
                                        isLastMonthSelected.value = false;
                                        isCurrentMonthSelected.value = false;
                                      },
                                      child: CustomSvgPicture(
                                        AppAsset.crossIcon,
                                        height: MySize.getScaledSizeHeight(20),
                                      ),
                                    )),
                              ],
                            ),
                          ),
                          Gap(MySize.getScaledSizeHeight(15)),
                          Gap(MySize.getScaledSizeHeight(5)),
                          Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal: MySize.getScaledSizeWidth(8)),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Expanded(
                                  child: OutlinedTextFormField(
                                    horizontalPadding: 0,
                                    fillColor: ColorConstant.textFieldColor,
                                    prefix: const CustomSvgPicture(
                                        AppAsset.customCalender),
                                    onTap: (context) async {
                                      DateTime initialDate = DateTime.now();
                                      DateTime? pickedDate =
                                          await showDatePicker(
                                        builder: (context, child) {
                                          return Theme(
                                              data: Theme.of(context).copyWith(
                                                  colorScheme:
                                                      const ColorScheme.light(
                                                    primary:
                                                        ColorConstant.signUp,
                                                    onPrimary: Colors.white,
                                                    onSurface: Colors
                                                        .black, // Month days , years
                                                  ),
                                                  textButtonTheme:
                                                      TextButtonThemeData(
                                                    style: TextButton.styleFrom(
                                                      foregroundColor: ColorConstant
                                                          .signUp, // ok , cancel    buttons
                                                    ),
                                                  ),
                                                  dialogBackgroundColor:
                                                      ColorConstant.white),
                                              child: child!);
                                        },
                                        context: context,
                                        initialDate: startDateController
                                                .text.isEmpty
                                            ? initialDate
                                            : DateFormat('yyyy-MM-dd').parse(
                                                startDateController.text),
                                        firstDate: DateTime(1800),
                                        lastDate: DateTime(2030),
                                      );
                                      if (pickedDate != null) {
                                        setState(() {
                                          startDateController.text =
                                              DateFormat('yyyy-MM-dd')
                                                  .format(pickedDate);
                                          initialDate = pickedDate;
                                        });
                                      }
                                    },
                                    controller: startDateController,
                                    hintText: "Start Date",
                                  ),
                                ),
                                Gap(MySize.getScaledSizeWidth(5)),
                                Expanded(
                                  child: OutlinedTextFormField(
                                    horizontalPadding: 0,
                                    fillColor: ColorConstant.textFieldColor,
                                    prefix: const CustomSvgPicture(
                                        AppAsset.customCalender),
                                    onTap: (context) async {
                                      DateTime initialDate = DateTime.now();
                                      DateTime? pickedDate =
                                          await showDatePicker(
                                        builder: (context, child) {
                                          return Theme(
                                              data: Theme.of(context).copyWith(
                                                  colorScheme:
                                                      const ColorScheme.light(
                                                    primary:
                                                        ColorConstant.signUp,
                                                    onPrimary: Colors.white,
                                                    onSurface: Colors
                                                        .black, // Month days , years
                                                  ),
                                                  textButtonTheme:
                                                      TextButtonThemeData(
                                                    style: TextButton.styleFrom(
                                                      foregroundColor: ColorConstant
                                                          .signUp, // ok , cancel    buttons
                                                    ),
                                                  ),
                                                  dialogBackgroundColor:
                                                      ColorConstant.white),
                                              child: child!);
                                        },
                                        context: context,
                                        initialDate: endDateController
                                                .text.isEmpty
                                            ? initialDate
                                            : DateFormat('yyyy-MM-dd')
                                                .parse(endDateController.text),
                                        firstDate: DateTime(1800),
                                        lastDate: DateTime(2030),
                                      );
                                      if (pickedDate != null) {
                                        setState(() {
                                          endDateController.text =
                                              DateFormat('yyyy-MM-dd')
                                                  .format(pickedDate);
                                          initialDate = pickedDate;
                                        });
                                      }
                                    },
                                    controller: endDateController,
                                    hintText: "End Date",
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Gap(MySize.getScaledSizeHeight(10)),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              ValueListenableBuilder(
                                valueListenable: is7DaySelected,
                                builder: (context, value, child) =>
                                    GestureDetector(
                                  onTap: () {
                                    is7DaySelected.value =
                                        !is7DaySelected.value;
                                    isCurrentMonthSelected.value = false;
                                    isLastMonthSelected.value = false;
                                    endDateController.text =
                                        DateFormat("yyyy-MM-dd")
                                            .format(DateTime.now());
                                    startDateController.text =
                                        DateFormat("yyyy-MM-dd").format(
                                            DateTime.now().subtract(
                                                const Duration(days: 7)));
                                    if (!is7DaySelected.value) {
                                      startDateController.clear();
                                      endDateController.clear();
                                    }
                                  },
                                  child: Container(
                                    height: MySize.getScaledSizeHeight(37),
                                    width: MySize.getScaledSizeWidth(98),
                                    decoration: BoxDecoration(
                                        gradient: LinearGradient(colors: [
                                          is7DaySelected.value
                                              ? ColorConstant.gradient1
                                              : ColorConstant.textFieldColor,
                                          is7DaySelected.value
                                              ? ColorConstant.gradient2
                                              : ColorConstant.textFieldColor,
                                        ]),
                                        color: is7DaySelected.value
                                            ? ColorConstant.primaryColor
                                            : ColorConstant.textFieldColor,
                                        borderRadius: const BorderRadius.all(
                                            Radius.circular(13))),
                                    child: Center(
                                        child: Padding(
                                      // 5.95px, 11.91px, 5.95px, 11.91px
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 13, vertical: 5.95),
                                      child: TypoGraphy.text("Last 7 Days",
                                          fontWeight: FontWeight.w500,
                                          color: is7DaySelected.value
                                              ? ColorConstant.white
                                              : ColorConstant.black,
                                          fontSize:
                                              MySize.getScaledSizeHeight(12)),
                                    )),
                                  ),
                                ),
                              ),
                              Gap(MySize.getScaledSizeWidth(14)),
                              ValueListenableBuilder(
                                valueListenable: isCurrentMonthSelected,
                                builder: (context, value, child) =>
                                    GestureDetector(
                                  onTap: () {
                                    isCurrentMonthSelected.value =
                                        !isCurrentMonthSelected.value;
                                    is7DaySelected.value = false;
                                    isLastMonthSelected.value = false;
                                    endDateController.text =
                                        DateFormat("yyyy-MM-dd")
                                            .format(DateTime.now());
                                    startDateController.text =
                                        DateFormat("yyyy-MM-dd").format(
                                            DateTime(DateTime.now().year,
                                                DateTime.now().month, 1));
                                    if (!isCurrentMonthSelected.value) {
                                      startDateController.clear();
                                      endDateController.clear();
                                    }
                                  },
                                  child: Container(
                                    height: MySize.getScaledSizeHeight(37),
                                    width: MySize.getScaledSizeWidth(104),
                                    decoration: BoxDecoration(
                                        gradient: LinearGradient(colors: [
                                          isCurrentMonthSelected.value
                                              ? ColorConstant.gradient1
                                              : ColorConstant.textFieldColor,
                                          isCurrentMonthSelected.value
                                              ? ColorConstant.gradient2
                                              : ColorConstant.textFieldColor,
                                        ]),
                                        color: isCurrentMonthSelected.value
                                            ? ColorConstant.primaryColor
                                            : ColorConstant.textFieldColor,
                                        borderRadius: const BorderRadius.all(
                                            Radius.circular(13))),
                                    child: Center(
                                        child: TypoGraphy.text("Current Month",
                                            fontWeight: FontWeight.w500,
                                            color: isCurrentMonthSelected.value
                                                ? ColorConstant.white
                                                : ColorConstant.black,
                                            fontSize:
                                                MySize.getScaledSizeHeight(
                                                    12))),
                                  ),
                                ),
                              ),
                              Gap(MySize.getScaledSizeWidth(14)),
                              ValueListenableBuilder(
                                valueListenable: isLastMonthSelected,
                                builder: (context, value, child) =>
                                    GestureDetector(
                                  onTap: () {
                                    isLastMonthSelected.value =
                                        !isLastMonthSelected.value;
                                    is7DaySelected.value = false;
                                    isCurrentMonthSelected.value = false;
                                    DateTime now = DateTime.now();
                                    DateTime firstDayLastMonth =
                                        DateTime(now.year, now.month - 1, 1);
                                    DateTime lastDayLastMonth =
                                        DateTime(now.year, now.month, 0);
                                    startDateController.text =
                                        DateFormat('yyyy-MM-dd')
                                            .format(firstDayLastMonth);
                                    endDateController.text =
                                        DateFormat('yyyy-MM-dd')
                                            .format(lastDayLastMonth);
                                    if (!isLastMonthSelected.value) {
                                      startDateController.clear();
                                      endDateController.clear();
                                    }
                                  },
                                  child: Container(
                                    height: MySize.getScaledSizeHeight(37),
                                    width: MySize.getScaledSizeWidth(98),
                                    decoration: BoxDecoration(
                                        gradient: LinearGradient(colors: [
                                          isLastMonthSelected.value
                                              ? ColorConstant.gradient1
                                              : ColorConstant.textFieldColor,
                                          isLastMonthSelected.value
                                              ? ColorConstant.gradient2
                                              : ColorConstant.textFieldColor,
                                        ]),
                                        color: isLastMonthSelected.value
                                            ? ColorConstant.primaryColor
                                            : ColorConstant.textFieldColor,
                                        borderRadius: const BorderRadius.all(
                                            Radius.circular(13))),
                                    child: Center(
                                        child: TypoGraphy.text("Last Month",
                                            fontWeight: FontWeight.w500,
                                            color: isLastMonthSelected.value
                                                ? ColorConstant.white
                                                : ColorConstant.black,
                                            fontSize:
                                                MySize.getScaledSizeHeight(
                                                    12))),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          OutlinedTextFormField(
                            onTap: (context) async {
                              final Map<Centers, ValueNotifier<bool>>
                                  centerSelectionMap = {};
                              for (int i = 0; i < assignCenters.length; i++) {
                                centerSelectionMap[Centers(
                                  id: assignCenters[i].id,
                                  name: assignCenters[i].name,
                                )] = isCenterSelected[i];
                              }

                              for (int i = 0;
                                  i < centerSelectionMap.length;
                                  i++) {
                                centerSelectionMap.values.elementAt(i).value =
                                    selectedCenters
                                        .map((e) => e.id)
                                        .toList()
                                        .contains(centerSelectionMap.keys
                                            .elementAt(i)
                                            .id);
                              }
                              if (assignCenters.length == 1) {
                                centerSelectionMap.values.elementAt(0).value =
                                    assignCenters
                                        .map((e) => e.id)
                                        .toList()
                                        .contains(centerSelectionMap.keys
                                            .elementAt(0)
                                            .id);
                              }

                              await MultipleSelectionBottomSheet.assignCenter(
                                  context,
                                  isSelectedList: centerSelectionMap,
                                  onTap: (List<Centers> centers) {
                                if (centers.isEmpty) {
                                  centerController.clear();
                                }

                                centerController.text =
                                    centers.map((e) => e.name).join(",");
                                selectedCenterIDs =
                                    centers.map((e) => e.id).toList();
                                selectedCenters = centers;
                              },
                                  centers: assignCenters,
                                  isSelectAll: ValueNotifier(false));
                            },
                            suffix: Padding(
                              padding: const EdgeInsets.only(right: 5.0),
                              child: Icon(
                                size: MySize.getScaledSizeHeight(25),
                                Icons.keyboard_arrow_down,
                                color: ColorConstant.primaryColor,
                              ),
                            ),
                            textStyle: TextStyle(
                                fontFamily:
                                    AppConstant.familyConstant.poppinsSemiBold,
                                color: ColorConstant.signUp,
                                fontWeight: FontWeight.w500,
                                fontSize: MySize.getScaledSizeHeight(14)),
                            isRequired: true,
                            isOnboard: true,
                            controller: centerController,
                            hintText: "Select Center",
                          ),
                          Gap(MySize.getScaledSizeHeight(20)),
                          AppElevatedButton(
                            width: MySize.getScaledSizeWidth(260),
                            TypoGraphy.text("Download Report",
                                level: 2,
                                fontWeight: FontWeight.w600,
                                color: ColorConstant.white),
                            onPressed: () {
                              onTap(selectedCenterIDs);
                            },
                          )
                        ],
                      ),
                    ),
                  ),
                ));
          },
        );
      },
    );
  }

  static downloadAlert(BuildContext context, {required void Function() onTap}) {
    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
            child: Align(
          child: Material(
            color: ColorConstant.white,
            elevation: 10,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            child: SizedBox(
              height: MySize.getScaledSizeHeight(450),
              width: MySize.getScaledSizeWidth(322),
              child: Column(
                children: [
                  Gap(MySize.getScaledSizeHeight(20)),
                  const CustomSvgPicture(AppAsset.downloadAlert),
                  Gap(MySize.getScaledSizeHeight(15)),
                  TypoGraphy.text("Download Successful",
                      fontSize: MySize.getScaledSizeHeight(20),
                      fontWeight: FontWeight.w700),
                  Gap(MySize.getScaledSizeHeight(7)),
                  TypoGraphy.text(
                      "Attendance Report has been\n successfully downloaded in your device",
                      level: 1,
                      color: ColorConstant.gray,
                      fontWeight: FontWeight.w500),
                  Gap(MySize.getScaledSizeHeight(25)),
                  GestureDetector(
                    onTap: onTap,
                    child: GradientText(
                        gradient: const LinearGradient(colors: [
                          ColorConstant.white,
                          ColorConstant.primaryColor,
                        ]),
                        child: TypoGraphy.text("Share Now",
                            level: 1,
                            fontWeight: FontWeight.w700,
                            color: ColorConstant.primaryColor)),
                  ),
                  Gap(MySize.getScaledSizeHeight(25)),
                  AppElevatedButton(
                    width: MySize.getScaledSizeWidth(280),
                    TypoGraphy.text("Okay",
                        level: 3, color: ColorConstant.white),
                    onPressed: () => context.pop(),
                  )
                ],
              ),
            ),
          ),
        ));
      },
    );
  }

  static updateCenterAndBatch(
    BuildContext context, {
    required List<int> selectedCenterIDs,
    Centers? selectedCenterData,
    Batches? selectedBatchData,
    required List<Centers> assignCenters,
    required List<Batches> assignBatch,
    required TextEditingController centerController,
    required TextEditingController batchController,
    required void Function(Centers center, Batches batches) onTap,
  }) async {
    context.read<GetCenterBatchBloc>().add(GetBatches(id: selectedCenterIDs));

    assignBatch.add(selectedBatchData!);
    centerController.text = selectedCenterData!.name;
    batchController.text = selectedBatchData.name;
    showDialog(
      context: context,
      builder: (context) {
        return BlocProvider(
          create: (context) => GetCenterBatchBloc(),
          child: Builder(builder: (context) {
            return BlocListener<GetCenterBatchBloc, GetCenterBatchState>(
              listener: (BuildContext context, GetCenterBatchState state) {
                if (state is GetBatchSuccess) {
                  assignBatch.clear();
                  assignBatch.addAll(state.getBatchesModel.batchData.batches);
                  if (state.getBatchesModel.batchData.batches.length == 1) {
                    selectedBatchData =
                        state.getBatchesModel.batchData.batches.first;
                    batchController.text =
                        state.getBatchesModel.batchData.batches[0].name;
                  } else {
                    batchController.clear();
                  }
                }
              },
              child: PopScope(
                  child: Align(
                child: Material(
                  color: ColorConstant.white,
                  elevation: 10,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: SizedBox(
                    height: MySize.getScaledSizeHeight(350),
                    width: MySize.getScaledSizeWidth(322),
                    child: Column(
                      children: [
                        Gap(MySize.getScaledSizeHeight(15)),
                        TypoGraphy.text("Center & Batch Info", level: 2),
                        Gap(MySize.getScaledSizeHeight(20)),
                        OutlinedTextFormField(
                          onTap: (context) {
                            SingleSelectionBottomSheet.selectCenter(
                              context,
                              onTap: (Centers? selectedCenter) {
                                centerController.text = selectedCenter!.name;
                                selectedCenterData = selectedCenter;
                                selectedCenterIDs.clear();
                                selectedCenterIDs.add(selectedCenter.id);
                                context
                                    .read<GetCenterBatchBloc>()
                                    .add(GetBatches(id: selectedCenterIDs));
                              },
                              centers: assignCenters,
                              initialSelectedCenter: selectedCenterData,
                            );
                          },
                          suffix: Padding(
                            padding: EdgeInsets.only(
                                right: MySize.getScaledSizeWidth(5)),
                            child: Icon(
                              size: MySize.getScaledSizeHeight(25),
                              Icons.keyboard_arrow_down,
                              color: ColorConstant.primaryColor,
                            ),
                          ),
                          textStyle: TextStyle(
                              fontFamily:
                                  AppConstant.familyConstant.poppinsSemiBold,
                              color: ColorConstant.signUp,
                              fontWeight: FontWeight.w500,
                              fontSize: MySize.getScaledSizeHeight(14)),
                          isRequired: true,
                          isOnboard: true,
                          controller: centerController,
                          hintText: "Assign Center",
                        ),
                        Gap(MySize.getScaledSizeHeight(10)),
                        OutlinedTextFormField(
                          onTap: (context) {
                            if (centerController.text.isNotEmpty) {
                              SingleSelectionBottomSheet.selectBatch(
                                context,
                                onTap: (Batches? selectedBatch) {
                                  batchController.text = selectedBatch!.name;
                                  selectedBatchData = selectedBatch;
                                },
                                batches: assignBatch,
                                initialSelectedBatch: selectedBatchData,
                              );
                            } else {
                              ToastUtils.showFailed(
                                  message: "Please Select Center first");
                            }
                          },
                          suffix: Padding(
                            padding: EdgeInsets.only(
                                right: MySize.getScaledSizeWidth(5)),
                            child: Icon(
                              size: MySize.getScaledSizeHeight(25),
                              Icons.keyboard_arrow_down,
                              color: ColorConstant.primaryColor,
                            ),
                          ),
                          textStyle: TextStyle(
                              fontFamily:
                                  AppConstant.familyConstant.poppinsSemiBold,
                              color: ColorConstant.signUp,
                              fontWeight: FontWeight.w500,
                              fontSize: MySize.getScaledSizeHeight(14)),
                          isRequired: true,
                          isOnboard: true,
                          controller: batchController,
                          hintText: "Assign Batch",
                        ),
                        const Expanded(child: SizedBox.shrink()),
                        Padding(
                          padding: EdgeInsets.only(
                              bottom: MySize.getScaledSizeHeight(18)),
                          child: AppElevatedButton(
                            width: MySize.getScaledSizeWidth(250),
                            TypoGraphy.text("Update Center & Batch",
                                level: 3, color: ColorConstant.white),
                            onPressed: () {
                              onTap(selectedCenterData!, selectedBatchData!);
                              context.pop();
                            },
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              )),
            );
          }),
        );
      },
    );
  }

  static showCustomDatePicker(BuildContext context,
      {required DateTime startDate,
      required DateTime endDate,
      required void Function(DateTime startDate, DateTime endDate)
          onTap}) async {
    DateTime selectedStartDate = startDate;
    DateTime selectedEndDate = endDate;
    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) => PopScope(
              child: Align(
            child: Material(
              color: ColorConstant.white,
              elevation: 10,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15),
              ),
              child: SizedBox(
                  height: MySize.getScaledSizeHeight(400),
                  width: MySize.getScaledSizeWidth(322),
                  child: Padding(
                    padding: EdgeInsets.all(MySize.size8 ?? 8),
                    child: Column(
                      children: [
                        SfDateRangePicker(
                          selectionColor: ColorConstant.gradient1,
                          todayHighlightColor: ColorConstant.gradient1,
                          rangeTextStyle:
                              const TextStyle(color: ColorConstant.white),
                          rangeSelectionColor:
                              const Color(0XFF047FDD).withOpacity(0.5),
                          initialSelectedRange: PickerDateRange(
                            startDate,
                            endDate,
                          ),
                          initialSelectedDate: startDate,
                          initialDisplayDate: endDate,
                          startRangeSelectionColor: ColorConstant.signUp,
                          endRangeSelectionColor: ColorConstant.signUp,
                          onSelectionChanged:
                              (DateRangePickerSelectionChangedArgs args) {
                            final PickerDateRange dateRange = args.value;
                            if (args.value.startDate != null &&
                                args.value.endDate != null) {
                              setState(() {
                                selectedStartDate = dateRange.startDate!;
                                selectedEndDate = dateRange.endDate!;
                              });
                            }
                          },
                          selectionMode: DateRangePickerSelectionMode.range,
                          headerStyle: const DateRangePickerHeaderStyle(
                              textAlign: TextAlign.center,
                              backgroundColor: ColorConstant.transparent),
                          backgroundColor: ColorConstant.white,
                        ),
                        Padding(
                          padding: EdgeInsets.only(
                              right: MySize.getScaledSizeWidth(18)),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              GradientText(
                                gradient: const LinearGradient(colors: [
                                  ColorConstant.white,
                                  ColorConstant.primaryColor,
                                ]),
                                child: GestureDetector(
                                  onTap: () => context.pop(),
                                  child: TypoGraphy.text(
                                    "Cancel",
                                    color: ColorConstant.primaryColor,
                                    fontSize: MySize.getScaledSizeHeight(20),
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                              Gap(MySize.getScaledSizeWidth(20)),
                              Padding(
                                padding: EdgeInsets.only(
                                    right: MySize.getScaledSizeWidth(8)),
                                child: GradientText(
                                  gradient: const LinearGradient(colors: [
                                    ColorConstant.white,
                                    ColorConstant.primaryColor,
                                  ]),
                                  child: GestureDetector(
                                    onTap: () {
                                      onTap(
                                        selectedStartDate,
                                        selectedEndDate,
                                      );
                                      context.pop();
                                    },
                                    child: TypoGraphy.text(
                                      "Ok",
                                      color: ColorConstant.primaryColor,
                                      fontSize: MySize.getScaledSizeHeight(20),
                                      fontWeight: FontWeight.w700,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  )),
            ),
          )),
        );
      },
    );
  }

  static upcomingDuesAlert(BuildContext context,
      {required String name,
      required String batchName,
      required int id,
      required int installmentCount,
      void Function()? activeTap,
      void Function()? presentTap,
      required File profileImage,
      required String startTime,
      required String planName,
      required String planStatus,
      required int amount,
      required DateTime dueDate,
      required String endTime}) {
    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
            child: Align(
          child: Material(
            color: ColorConstant.white,
            elevation: 10,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            child: SizedBox(
              height: MySize.getScaledSizeHeight(
                  planStatus == "Installment" ? 470 : 430),
              width: MySize.getScaledSizeWidth(314),
              child: Column(
                children: [
                  Padding(
                    padding: EdgeInsets.only(
                        right: MySize.getScaledSizeWidth(12),
                        top: MySize.getScaledSizeHeight(12)),
                    child: Align(
                        alignment: Alignment.topRight,
                        child: GestureDetector(
                          onTap: () {
                            context.pop();
                          },
                          child: CircleAvatar(
                            radius: 12,
                            backgroundColor: ColorConstant.textFieldColor,
                            child: CustomSvgPicture(
                              AppAsset.crossIcon,
                              height: MySize.getScaledSizeHeight(15),
                            ),
                          ),
                        )),
                  ),
                  CircleAvatar(
                      radius: 35,
                      backgroundColor: profileImage.path != AppAsset.studentIcon
                          ? ColorConstant.transparent
                          : ColorConstant.studentImage,
                      child: ClipOval(
                        clipBehavior: Clip.hardEdge,
                        child: profileImage.path != AppAsset.studentIcon
                            ? Image.network(
                                profileImage.path,
                                fit: BoxFit.cover,
                                height: MySize.getScaledSizeHeight(70),
                                width: MySize.getScaledSizeWidth(70),
                              )
                            : Padding(
                                padding: EdgeInsets.only(
                                    top: MySize.getScaledSizeHeight(8)),
                                child: CustomSvgPicture(
                                  AppAsset.studentIcon,
                                  height: MySize.getScaledSizeHeight(42),
                                ),
                              ),
                      )),
                  Gap(MySize.getScaledSizeHeight(8)),
                  TypoGraphy.text(name, level: 3, fontWeight: FontWeight.w500),
                  Gap(MySize.getScaledSizeHeight(3)),
                  TypoGraphy.text(batchName,
                      fontSize: MySize.getScaledSizeHeight(12),
                      fontWeight: FontWeight.w500),
                  Gap(MySize.getScaledSizeHeight(5)),
                  TypoGraphy.text("($startTime - $endTime)",
                      color: ColorConstant.gray,
                      fontSize: MySize.getScaledSizeHeight(12),
                      fontWeight: FontWeight.w500),
                  Gap(MySize.getScaledSizeHeight(15)),
                  TypoGraphy.text(planName,
                      level: 1, fontWeight: FontWeight.w500),
                  Gap(MySize.getScaledSizeHeight(3)),
                  TypoGraphy.text(
                      fontSize: MySize.getScaledSizeHeight(12),
                      fontWeight: FontWeight.w400,
                      "${dueDate.day} ${DateFormat("MMMM").format(dueDate)}"),
                  Gap(MySize.getScaledSizeHeight(20)),
                  if (planStatus == "Installment")
                    Container(
                      height: MySize.getScaledSizeHeight(45),
                      width: MySize.getScaledSizeWidth(278),
                      decoration: BoxDecoration(
                          borderRadius:
                              const BorderRadius.all(Radius.circular(17)),
                          color: ColorConstant.white,
                          border:
                              Border.all(color: ColorConstant.textFieldColor)),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          TypoGraphy.text("Installment No :  ",
                              level: 1,
                              fontWeight: FontWeight.w500,
                              color: ColorConstant.gray),
                          TypoGraphy.text("01 / $installmentCount",
                              level: 1,
                              fontWeight: FontWeight.w500,
                              color: ColorConstant.black)
                        ],
                      ),
                    ),
                  if (planStatus == "Installment")
                    Gap(MySize.getScaledSizeHeight(7)),
                  Container(
                    height: MySize.getScaledSizeHeight(45),
                    width: MySize.getScaledSizeWidth(278),
                    decoration: BoxDecoration(
                        borderRadius:
                            const BorderRadius.all(Radius.circular(17)),
                        color: ColorConstant.white,
                        border:
                            Border.all(color: ColorConstant.textFieldColor)),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        TypoGraphy.text(
                            planStatus == "Installment"
                                ? "Installment Amount :  "
                                : "Plan Amount : ",
                            level: 1,
                            fontWeight: FontWeight.w500,
                            color: ColorConstant.gray),
                        TypoGraphy.text("₹ $amount",
                            level: 1,
                            fontWeight: FontWeight.w500,
                            color: ColorConstant.black)
                      ],
                    ),
                  ),
                  Gap(MySize.getScaledSizeHeight(37)),
                  AppElevatedButton(
                    width: MySize.getScaledSizeWidth(278),
                    TypoGraphy.text("Okay",
                        level: 3, color: ColorConstant.white),
                    onPressed: () => context.pop(),
                  )
                ],
              ),
            ),
          ),
        ));
      },
    );
  }

  static extraCharge(BuildContext context,
      {required List<ExtraCharge> extraCharge}) {
    ScrollController scrollController = ScrollController();
    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
            child: Align(
          child: Material(
            color: ColorConstant.white,
            elevation: 10,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            child: SizedBox(
              width: MySize.getScaledSizeWidth(303),
              height: MySize.getScaledSizeHeight(300),
              child: Column(
                children: [
                  Gap(MySize.getScaledSizeHeight(10)),
                  TypoGraphy.text("Charge Details",
                      level: 1, fontSize: MySize.getScaledSizeHeight(16)),
                  Gap(MySize.getScaledSizeHeight(20)),
                  Expanded(
                      child: ScrollbarHelper(
                    scrollController: scrollController,
                    child: ListView(
                      controller: scrollController,
                      children: List.generate(
                        extraCharge.length,
                        (index) => Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: MySize.getScaledSizeWidth(12),
                              vertical: MySize.getScaledSizeHeight(5)),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              TypoGraphy.text(
                                  extraCharge[index].quantity > 1
                                      ? "${extraCharge[index].name} X ${extraCharge[index].quantity}"
                                      : extraCharge[index].name,
                                  level: 2,
                                  fontWeight: FontWeight.w500),
                              TypoGraphy.text("₹ ${extraCharge[index].amount}",
                                  level: 2, fontWeight: FontWeight.w500)
                            ],
                          ),
                        ),
                      ),
                    ),
                  )),
                  Gap(MySize.getScaledSizeHeight(10)),
                  Padding(
                    padding:
                        EdgeInsets.only(bottom: MySize.getScaledSizeHeight(10)),
                    child: AppElevatedButton(
                      width: MySize.getScaledSizeWidth(241),
                      TypoGraphy.text("Okay",
                          fontWeight: FontWeight.w700,
                          fontSize: MySize.getScaledSizeHeight(17),
                          color: ColorConstant.white),
                      onPressed: () {
                        context.pop();
                      },
                    ),
                  )
                ],
              ),
            ),
          ),
        ));
      },
    );
  }

  static reportSuccess(BuildContext context,
      {required void Function() onShareTap}) {
    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
          child: Align(
            child: Material(
              color: ColorConstant.white,
              elevation: 10,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15),
              ),
              child: SizedBox(
                width: MySize.getScaledSizeWidth(307),
                height: MySize.getScaledSizeHeight(369),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const CustomSvgPicture(AppAsset.attendanceReportSuccess),
                    Gap(MySize.getScaledSizeHeight(20)),
                    GestureDetector(
                      onTap: onShareTap,
                      child: GradientText(
                          gradient: const LinearGradient(colors: [
                            ColorConstant.white,
                            ColorConstant.primaryColor,
                          ]),
                          child: TypoGraphy.text("Share Now",
                              level: 1, color: ColorConstant.primaryColor)),
                    ),
                    Gap(MySize.getScaledSizeHeight(10)),
                    AppElevatedButton(
                      width: MySize.getScaledSizeWidth(235),
                      height: MySize.getScaledSizeHeight(47),
                      TypoGraphy.text("Done",
                          level: 3,
                          fontWeight: FontWeight.w600,
                          color: ColorConstant.white),
                      onPressed: context.pop,
                    )
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  static paymentChange(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
            child: Align(
          child: Material(
            color: ColorConstant.white,
            elevation: 10,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            child: SizedBox(
              height: MySize.getScaledSizeHeight(475),
              width: MySize.getScaledSizeWidth(318),
              child: Column(
                children: [
                  const Expanded(child: CustomSvgPicture(AppAsset.planChange)),
                  Padding(
                    padding: EdgeInsets.only(
                        bottom: MySize.getScaledSizeHeight(18),
                        right: MySize.getScaledSizeWidth(10)),
                    child: Row(
                      children: [
                        Expanded(
                          child: GradientText(
                            gradient: const LinearGradient(colors: [
                              ColorConstant.white,
                              ColorConstant.primaryColor
                            ]),
                            child: TypoGraphy.text("Not Received",
                                level: 2,
                                fontWeight: FontWeight.w500,
                                color: ColorConstant.primaryColor),
                          ),
                        ),
                        Expanded(
                          child: AppElevatedButton(
                            width: MySize.getScaledSizeWidth(158),
                            height: MySize.getScaledSizeHeight(50),
                            TypoGraphy.text("Fee Received",
                                level: 2,
                                fontWeight: FontWeight.w600,
                                color: ColorConstant.white),
                            onPressed: () {},
                          ),
                        )
                      ],
                    ),
                  )
                ],
              ),
            ),
          ),
        ));
      },
    );
  }

  static customStartCalender(BuildContext context,
      {required ValueNotifier<int> selectedDateNotifier,
      required void Function(int selectedNum) onTap}) {
    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
            child: Align(
          child: Material(
            color: ColorConstant.white,
            elevation: 10,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            child: SizedBox(
              height: MySize.getScaledSizeHeight(330),
              width: MySize.getScaledSizeWidth(318),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Gap(MySize.getScaledSizeHeight(12)),
                  ValueListenableBuilder(
                    valueListenable: selectedDateNotifier,
                    builder: (context, value, child) => Container(
                      height: MySize.getScaledSizeHeight(230),
                      width: MySize.getScaledSizeWidth(285),
                      decoration: const BoxDecoration(
                          color: Color(0XFF0B76C5),
                          borderRadius: BorderRadius.all(Radius.circular(15))),
                      child: Padding(
                        padding: EdgeInsets.all(MySize.size8 ?? 8),
                        child: GridView.builder(
                          shrinkWrap: true,
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 7,
                            crossAxisSpacing: 5,
                            mainAxisSpacing: 5,
                          ),
                          itemCount: 31,
                          itemBuilder: (context, index) {
                            bool isSelected = value == index + 1;
                            return Padding(
                                padding: EdgeInsets.only(
                                    left: MySize.getScaledSizeWidth(8)),
                                child: GestureDetector(
                                  onTap: () {
                                    selectedDateNotifier.value = index + 1;
                                  },
                                  child: Container(
                                    height: MySize.getScaledSizeHeight(20),
                                    width: MySize.getScaledSizeWidth(20),
                                    decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: isSelected
                                            ? Colors.white
                                            : const Color(0XFF0B76C5)),
                                    child: Center(
                                      child: TypoGraphy.text(
                                        '${index + 1}',
                                        level: 1,
                                        color: isSelected
                                            ? const Color(0XFF0B76C5)
                                            : ColorConstant.white,
                                      ),
                                    ),
                                  ),
                                ));
                          },
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: MySize.getScaledSizeHeight(20)),
                  AppElevatedButton(
                    width: MySize.getScaledSizeWidth(278),
                    TypoGraphy.text("Done",
                        level: 3, color: ColorConstant.white),
                    onPressed: () {
                      onTap(selectedDateNotifier.value);
                      context.pop();
                    },
                  )
                ],
              ),
            ),
          ),
        ));
      },
    );
  }

  static customEndCalender(BuildContext context,
      {required ValueNotifier<int> selectedDateNotifier,
      required ValueNotifier<int> startAt,
      required void Function(int selectedNum) onTap}) {
    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
            child: Align(
          child: Material(
            color: ColorConstant.white,
            elevation: 10,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            child: SizedBox(
              height: MySize.getScaledSizeHeight(330),
              width: MySize.getScaledSizeWidth(318),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Gap(MySize.getScaledSizeHeight(12)),
                  ValueListenableBuilder(
                    valueListenable: selectedDateNotifier,
                    builder: (context, value, child) => Container(
                      height: MySize.getScaledSizeHeight(230),
                      width: MySize.getScaledSizeWidth(285),
                      decoration: const BoxDecoration(
                          color: Color(0XFF0B76C5),
                          borderRadius: BorderRadius.all(Radius.circular(15))),
                      child: Padding(
                        padding: EdgeInsets.all(MySize.size8 ?? 8),
                        child: GridView.builder(
                          shrinkWrap: true,
                          gridDelegate:
                              const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 7,
                            crossAxisSpacing: 5,
                            mainAxisSpacing: 5,
                          ),
                          itemCount: 31,
                          itemBuilder: (context, index) {
                            int dateValue = index + 1;
                            bool isSelected = value == dateValue;
                            bool isBlackOut = dateValue <= startAt.value;
                            return Padding(
                                padding: EdgeInsets.only(
                                    left: MySize.getScaledSizeWidth(8)),
                                child: GestureDetector(
                                  onTap: isBlackOut
                                      ? null
                                      : () {
                                          selectedDateNotifier.value =
                                              dateValue;
                                        },
                                  child: Container(
                                    height: MySize.getScaledSizeHeight(20),
                                    width: MySize.getScaledSizeWidth(20),
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: isSelected
                                          ? Colors.white
                                          : const Color(0XFF0B76C5),
                                    ),
                                    child: Center(
                                      child: Opacity(
                                        opacity: isBlackOut ? 0.6 : 1,
                                        child: TypoGraphy.text(
                                          '${index + 1}',
                                          level: 1,
                                          color: isSelected
                                              ? const Color(0XFF0B76C5)
                                              : isBlackOut
                                                  ? ColorConstant.gray
                                                  : ColorConstant.white,
                                        ),
                                      ),
                                    ),
                                  ),
                                ));
                          },
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                  AppElevatedButton(
                    width: MySize.getScaledSizeWidth(278),
                    TypoGraphy.text("Done",
                        level: 3, color: ColorConstant.white),
                    onPressed: () {
                      onTap(selectedDateNotifier.value);
                      context.pop();
                    },
                  )
                ],
              ),
            ),
          ),
        ));
      },
    );
  }

  static customMonthYearCalender(BuildContext context,
      {required ValueNotifier<DateTime> selectedDate,
      required void Function(DateTime) onTap}) {
    List<int> availableYears =
        List.generate(4, (index) => DateTime.now().year - index);

    List<Map<String, dynamic>> getMonthItems(DateTime tempDate) {
      int maxMonth =
          tempDate.year == DateTime.now().year ? DateTime.now().month : 12;

      return List.generate(12, (index) {
        bool isSelectable = tempDate.year < DateTime.now().year ||
            (tempDate.year == DateTime.now().year && index + 1 <= maxMonth);

        return {
          'value': index + 1,
          'label': DateFormat("MMM").format(DateTime(0, index + 1)),
          'isSelectable': isSelectable,
        };
      });
    }

    ValueNotifier<DateTime> tempDate = ValueNotifier(selectedDate.value);

    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
          child: Align(
            child: Material(
              color: ColorConstant.white,
              elevation: 10,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15),
              ),
              child: SizedBox(
                height: MySize.getScaledSizeHeight(580),
                width: MySize.getScaledSizeWidth(318),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Gap(MySize.getScaledSizeHeight(15)),
                    const CustomSvgPicture(AppAsset.monthCalender),
                    Gap(MySize.getScaledSizeHeight(30)),
                    Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: MySize.getScaledSizeWidth(12)),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          GestureDetector(
                            onTap: () {
                              if (tempDate.value.year > availableYears.last) {
                                tempDate.value = DateTime(
                                    tempDate.value.year - 1,
                                    tempDate.value.month);
                              }
                            },
                            child: GradientText(
                              gradient: const LinearGradient(colors: [
                                ColorConstant.white,
                                ColorConstant.primaryColor,
                              ]),
                              child: Padding(
                                padding: EdgeInsets.symmetric(
                                    vertical: MySize.getScaledSizeHeight(8),
                                    horizontal: MySize.getScaledSizeWidth(8)),
                                child: const Icon(
                                  Icons.arrow_back_ios_new_rounded,
                                  size: 20,
                                  color: ColorConstant.primaryColor,
                                ),
                              ),
                            ),
                          ),
                          ValueListenableBuilder(
                            valueListenable: tempDate,
                            builder: (context, value, child) => TypoGraphy.text(
                                tempDate.value.year.toString(),
                                level: 2,
                                fontWeight: FontWeight.w500),
                          ),
                          GestureDetector(
                            onTap: () {
                              if (tempDate.value.year < DateTime.now().year) {
                                tempDate.value = DateTime(
                                    tempDate.value.year + 1,
                                    tempDate.value.month);
                              }
                            },
                            child: GradientText(
                              gradient: const LinearGradient(colors: [
                                ColorConstant.white,
                                ColorConstant.primaryColor,
                              ]),
                              child: Padding(
                                padding: EdgeInsets.symmetric(
                                    vertical: MySize.getScaledSizeHeight(8),
                                    horizontal: MySize.getScaledSizeWidth(8)),
                                child: const Icon(
                                  Icons.arrow_forward_ios_rounded,
                                  size: 20,
                                  color: ColorConstant.primaryColor,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Gap(MySize.getScaledSizeHeight(10)),
                    Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: MySize.getScaledSizeWidth(8)),
                      child: ValueListenableBuilder<DateTime>(
                        valueListenable: tempDate,
                        builder: (context, value, child) {
                          return GridView.builder(
                            shrinkWrap: true,
                            gridDelegate:
                                const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 3,
                              mainAxisSpacing: 8.0,
                              crossAxisSpacing: 8.0,
                              childAspectRatio: 2,
                            ),
                            itemCount: getMonthItems(value).length,
                            itemBuilder: (context, index) {
                              final month = getMonthItems(value)[index];
                              bool isSelected =
                                  tempDate.value.month == month['value'];
                              bool isSelectable = month['isSelectable'];

                              return GestureDetector(
                                onTap: isSelectable
                                    ? () {
                                        tempDate.value = DateTime(
                                          tempDate.value.year,
                                          month['value'],
                                        );
                                      }
                                    : null,
                                child: Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(27),
                                    gradient: isSelected && isSelectable
                                        ? const LinearGradient(colors: [
                                            ColorConstant.gradient1,
                                            ColorConstant.gradient2,
                                          ])
                                        : null,
                                    color: isSelectable
                                        ? (isSelected
                                            ? Colors.blue
                                            : Colors.grey[200])
                                        : Colors.grey[100],
                                  ),
                                  child: Center(
                                    child: TypoGraphy.text(
                                      month['label'],
                                      level: 2,
                                      fontWeight: FontWeight.w500,
                                      color: isSelectable
                                          ? (isSelected
                                              ? Colors.white
                                              : Colors.black)
                                          : Colors.grey[500],
                                    ),
                                  ),
                                ),
                              );
                            },
                          );
                        },
                      ),
                    ),

                    Gap(MySize.getScaledSizeHeight(20)),
                    Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: MySize.getScaledSizeWidth(15)),
                      child: AppElevatedButton(
                        TypoGraphy.text("Confirm",
                            level: 3, color: ColorConstant.white),
                        onPressed: () {
                          selectedDate.value = tempDate.value;
                          onTap(selectedDate.value);
                          context.pop();
                        },
                      ),
                    )
                    // Padding(
                    //   padding: const EdgeInsets.only(right: 10.0, bottom: 10.0),
                    //   child: Row(
                    //     mainAxisAlignment: MainAxisAlignment.end,
                    //     children: [
                    //       GradientText(
                    //           gradient: const LinearGradient(colors: [
                    //             ColorConstant.white,
                    //             ColorConstant.primaryColor
                    //           ]),
                    //           child: GestureDetector(
                    //             onTap: () {
                    //               context.pop();
                    //             },
                    //             child: TypoGraphy.text("Cancel",
                    //                 level: 3,
                    //                 color: ColorConstant.primaryColor),
                    //           )),
                    //       Gap(MySize.getScaledSizeWidth(10)),
                    //       GestureDetector(
                    //         onTap: () {
                    //           selectedDate.value = tempDate.value;
                    //           onTap(selectedDate.value);
                    //           context.pop();
                    //         },
                    //         child: GradientText(
                    //             gradient: const LinearGradient(colors: [
                    //               ColorConstant.white,
                    //               ColorConstant.primaryColor
                    //             ]),
                    //             child: TypoGraphy.text("OK",
                    //                 level: 3,
                    //                 color: ColorConstant.primaryColor)),
                    //       ),
                    //     ],
                    //   ),
                    // ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  static coachLoginPopup(BuildContext context,
      {required void Function() onTap}) {
    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
            child: Align(
          child: Material(
            color: ColorConstant.white,
            elevation: 10,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            child: SizedBox(
              height: MySize.getScaledSizeHeight(478),
              width: MySize.getScaledSizeWidth(300),
              child: Column(
                children: [
                  const CustomSvgPicture(AppAsset.coachLogin),
                  Gap(MySize.getScaledSizeHeight(50)),
                  SizedBox(
                    width: MySize.getScaledSizeWidth(200),
                    child: TypoGraphy.text("Your number is linked as a Coach",
                        level: 3, fontWeight: FontWeight.w600),
                  ),
                  Gap(MySize.getScaledSizeHeight(15)),
                  SizedBox(
                    width: MySize.getScaledSizeWidth(180),
                    child: TypoGraphy.text(
                        "Kindly login as a Coach by entering password ",
                        level: 1,
                        color: ColorConstant.gray,
                        fontWeight: FontWeight.w400),
                  ),
                  Gap(MySize.getScaledSizeHeight(50)),
                  Padding(
                    padding: EdgeInsets.symmetric(
                        horizontal: MySize.getScaledSizeWidth(40)),
                    child: AppElevatedButton(
                      TypoGraphy.text("Login As Coach",
                          level: 3, color: ColorConstant.white),
                      onPressed: () {
                        onTap();
                      },
                    ),
                  )
                ],
              ),
            ),
          ),
        ));
      },
    );
  }
  static appBackDialog(BuildContext context,
      {bool isForAssessment = false,
      bool isForFeeReminder = false,
      void Function()? yesTap,
      void Function()? noTap}) {
    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
            child: Align(
          child: Material(
            color: ColorConstant.white,
            elevation: 10,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            child: SizedBox(
              width: MySize.getScaledSizeWidth(286),
              child: Padding(
                padding:
                    EdgeInsets.only(bottom: MySize.getScaledSizeHeight(10)),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Gap(MySize.getScaledSizeHeight(15)),
                    SizedBox(
                      width: MySize.getScaledSizeWidth(250),
                      child: GradientText(
                        gradient: const LinearGradient(colors: [
                          ColorConstant.white,
                          ColorConstant.primaryColor,
                        ]),
                        child: TypoGraphy.text(
                            textAlign: TextAlign.start,
                            isForAssessment
                                ? "Do you want to resend assessment report ?"
                                : isForFeeReminder
                                    ? "Do you want to send fee reminder ?"
                                    : "Do you want to leave ?",
                            level: 2,
                            fontWeight: FontWeight.w600),
                      ),
                    ),
                    Gap(MySize.getScaledSizeHeight(15)),
                    SizedBox(
                      width: MySize.getScaledSizeWidth(244),
                      child: TypoGraphy.text(
                          textAlign: TextAlign.start,
                          isForAssessment
                              ? "Do you really want to send the assessment report in Whatsapp"
                              : isForFeeReminder
                                  ? "Do you really want to send the fee reminder in Whatsapp"
                                  : "Are you sure want to leave as the data will not be saved once you leaved .",
                          maxLines: 2,
                          fontSize: MySize.getScaledSizeHeight(12),
                          color: ColorConstant.gray,
                          fontWeight: FontWeight.w400),
                    ),
                    Gap(MySize.getScaledSizeHeight(26)),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        GradientText(
                          gradient: const LinearGradient(colors: [
                            ColorConstant.white,
                            ColorConstant.primaryColor,
                          ]),
                          child: GestureDetector(
                            onTap: () {
                              if (yesTap == null) {
                                context.pop();
                                context.pop();
                              } else {
                                yesTap();
                              }
                            },
                            child: TypoGraphy.text(
                                textAlign: TextAlign.start,
                                "Yes",
                                fontSize: MySize.getScaledSizeHeight(15),
                                color: ColorConstant.primaryColor,
                                fontWeight: FontWeight.w700),
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            if (noTap == null) {
                              context.pop();
                            } else {
                              noTap();
                            }
                          },
                          child: Container(
                            decoration: const BoxDecoration(
                                color: ColorConstant.gradient1,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(60)),
                                gradient: LinearGradient(colors: [
                                  ColorConstant.gradient1,
                                  ColorConstant.gradient2,
                                ])),
                            child: Padding(
                              padding: EdgeInsets.symmetric(
                                  horizontal: MySize.getScaledSizeWidth(65),
                                  vertical: MySize.getScaledSizeHeight(10)),
                              child: TypoGraphy.text(
                                  textAlign: TextAlign.start,
                                  "No",
                                  level: 2,
                                  color: ColorConstant.white,
                                  fontWeight: FontWeight.w600),
                            ),
                          ),
                        )
                      ],
                    )
                  ],
                ),
              ),
            ),
          ),
        ));
      },
    );
  }

  static exploreFormDialog(BuildContext context,
      {required void Function() onExploreTap}) {
    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
            child: Align(
          child: Material(
            color: ColorConstant.white,
            elevation: 10,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            child: SizedBox(
              height: MySize.getScaledSizeHeight(435),
              width: MySize.getScaledSizeWidth(286),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Gap(MySize.getScaledSizeHeight(15)),
                  const Align(
                      alignment: Alignment.center,
                      child: CustomSvgPicture(AppAsset.exploreSvg)),
                  Gap(MySize.getScaledSizeHeight(15)),
                  Padding(
                    padding: EdgeInsets.symmetric(
                        horizontal: MySize.getScaledSizeWidth(16)),
                    child: TypoGraphy.text("Get More Admission", level: 2),
                  ),
                  Gap(MySize.getScaledSizeHeight(15)),
                  Padding(
                    padding: EdgeInsets.symmetric(
                        horizontal: MySize.getScaledSizeWidth(16)),
                    child: SizedBox(
                      width: MySize.getScaledSizeWidth(244),
                      child: TypoGraphy.text(
                          textAlign: TextAlign.start,
                          "Introducing digital admission form . Now just share the QR code for admission form fill up .",
                          maxLines: 3,
                          fontSize: MySize.getScaledSizeHeight(12),
                          color: ColorConstant.gray,
                          fontWeight: FontWeight.w400),
                    ),
                  ),
                  Gap(MySize.getScaledSizeHeight(22)),
                  Align(
                    alignment: Alignment.center,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        GradientText(
                          gradient: const LinearGradient(colors: [
                            ColorConstant.white,
                            ColorConstant.primaryColor,
                          ]),
                          child: GestureDetector(
                            onTap: () async {
                              await StorageManager().saveBoolData(
                                  AppConstant.storageConstant.exploreDone,
                                  true);
                              context.pop();
                            },
                            child: TypoGraphy.text(
                                textAlign: TextAlign.start,
                                "Skip",
                                fontSize: MySize.getScaledSizeHeight(15),
                                color: ColorConstant.primaryColor,
                                fontWeight: FontWeight.w700),
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            onExploreTap();
                          },
                          child: Container(
                            decoration: const BoxDecoration(
                                color: ColorConstant.gradient1,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(60)),
                                gradient: LinearGradient(colors: [
                                  ColorConstant.gradient1,
                                  ColorConstant.gradient2,
                                ])),
                            child: Padding(
                              padding: EdgeInsets.symmetric(
                                  horizontal: MySize.getScaledSizeWidth(65),
                                  vertical: MySize.getScaledSizeHeight(10)),
                              child: TypoGraphy.text(
                                  textAlign: TextAlign.start,
                                  "Explore",
                                  level: 2,
                                  color: ColorConstant.white,
                                  fontWeight: FontWeight.w600),
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                  Gap(MySize.getScaledSizeHeight(15)),
                ],
              ),
            ),
          ),
        ));
      },
    );
  }

  static declinePopup(BuildContext context,
      {required void Function(String reason) onTap}) {
    TextEditingController reasonController = TextEditingController();
    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
            child: Align(
          child: Material(
            color: ColorConstant.white,
            elevation: 10,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            child: SizedBox(
              height: MySize.getScaledSizeHeight(460),
              width: MySize.getScaledSizeWidth(300),
              child: Column(
                children: [
                  Gap(MySize.getScaledSizeHeight(40)),
                  const CustomSvgPicture(AppAsset.decline),
                  Gap(MySize.getScaledSizeHeight(20)),
                  Padding(
                    padding: EdgeInsets.symmetric(
                        horizontal: MySize.getScaledSizeHeight(8)),
                    child: OutlinedTextFormField(
                      controller: reasonController,
                      minLines: 5,
                      maxLines: 15,
                      border: 12,
                      isOnboard: true,
                      hintText: "Write The reason of decline ( optional)",
                      hintStyle: GoogleFonts.poppins(
                          textStyle: TextStyle(
                              color: ColorConstant.gray,
                              fontWeight: FontWeight.w400,
                              fontSize: MySize.getScaledSizeHeight(12))),
                    ),
                  ),
                  Gap(MySize.getScaledSizeHeight(30)),
                  Padding(
                    padding: EdgeInsets.symmetric(
                        horizontal: MySize.getScaledSizeWidth(12)),
                    child: GestureDetector(
                      onTap: () {
                        onTap(reasonController.text);
                      },
                      child: Container(
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(100),
                            border: Border.all(
                                color: ColorConstant.absentDarkColor)),
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: MySize.getScaledSizeWidth(100),
                              vertical: MySize.getScaledSizeHeight(10)),
                          child: TypoGraphy.text("Decline",
                              color: ColorConstant.absentDarkColor, level: 3),
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
          ),
        ));
      },
    );
  }

  static imagePopUp(BuildContext context,
      {required String image, required void Function() onDownloadTap}) {
    showDialog(
      context: context,
      builder: (context) {
        return PopScope(
            child: Align(
          child: Material(
            color: ColorConstant.transparent,
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(
                  vertical: MySize.getScaledSizeHeight(50)),
              child: SizedBox(
                height: double.infinity,
                width: double.infinity,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                          color: ColorConstant.white,
                          borderRadius: BorderRadius.circular(21)),
                      height: MySize.getScaledSizeHeight(400),
                      width: MySize.getScaledSizeWidth(286),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: Image.network(
                          image,
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),
                    Gap(MySize.getScaledSizeHeight(17)),
                    Container(
                      width: MySize.getScaledSizeWidth(286),
                      decoration: const BoxDecoration(
                          color: ColorConstant.dashBoardTextField,
                          borderRadius: BorderRadius.all(Radius.circular(15))),
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                            vertical: MySize.getScaledSizeHeight(12),
                            horizontal: MySize.getScaledSizeWidth(45)),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            GestureDetector(
                              onTap: () {
                                onDownloadTap();
                              },
                              child: GradientText(
                                  gradient: const LinearGradient(colors: [
                                    ColorConstant.white,
                                    ColorConstant.primaryColor
                                  ]),
                                  child: CustomSvgPicture(
                                      height: MySize.getScaledSizeHeight(25),
                                      AppAsset.downloadAssessment)),
                            ),
                            GestureDetector(
                              onTap: () {
                                context.pop();
                              },
                              child: TypoGraphy.text("Cancel",
                                  level: 3,
                                  fontWeight: FontWeight.w600,
                                  color: ColorConstant.black),
                            ),
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
        ));
      },
    );
  }
}
