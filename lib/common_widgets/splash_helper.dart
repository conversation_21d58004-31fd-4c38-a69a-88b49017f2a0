import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:khelnet/global/constants/app_constant.dart';
import 'package:khelnet/utils/constants/routes_constant.dart';
import 'package:khelnet/utils/extension/navigation_extension.dart';
import 'package:khelnet/utils/manager/storage_manager.dart';

import '../global/constants/size.dart';
import '../utils/constants/color_constant.dart';
import '../utils/theme/typography.dart';
import 'buttons/app_elevated_button.dart';
import 'custom_svg_picture.dart';

class SplashHelper extends StatelessWidget {
  final String image, titleText, descriptionText, buttonText, route;
  final bool isPng;

  const SplashHelper(
      {super.key,
      required this.image,
      this.isPng = false,
      required this.titleText,
      required this.descriptionText,
      required this.buttonText,
      required this.route});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Stack(
        children: [
          Positioned.fill(
              child: !isPng
                  ? CustomSvgPicture(
                      image,
                      boxFit: BoxFit.fill,
                    )
                  : Image.asset(
                      image,
                      fit: BoxFit.fill,
                    )),
          Positioned.fill(
            top: MySize.getScaledSizeHeight(482),
            child: Container(
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(28),
                  topRight: Radius.circular(28),
                ),
              ),
              child: Column(
                children: [
                  Gap(MySize.getScaledSizeHeight(50)),
                  TypoGraphy.text(
                    titleText,
                    level: 3,
                  ),
                  Gap(MySize.getScaledSizeHeight(20)),
                  TypoGraphy.text(descriptionText,
                      level: 2,
                      textAlign: TextAlign.center,
                      fontWeight: FontWeight.w500,
                      color: ColorConstant.greyColor),
                  Gap(MySize.getScaledSizeHeight(65)),
                  TextButton(
                      onPressed: () async {
                        await StorageManager().saveBoolData(
                            AppConstant.storageConstant.coachFlowDone, true);
                        await StorageManager().saveBoolData(
                            AppConstant.storageConstant.planAndChargeFlow,
                            true);
                        await StorageManager().saveBoolData(
                            AppConstant.storageConstant.centerFlowDone, true);
                        await StorageManager().saveBoolData(
                            AppConstant.storageConstant.studentFlowDone, true);
                        if (context.mounted) {
                          context.pushNamedAndRemoveUntil(
                              RouteConstant.homeScreen);
                        }
                      },
                      child: TypoGraphy.text("Skip",
                          color: ColorConstant.primaryColor, level: 2)),
                  Gap(MySize.getScaledSizeHeight(17)),
                  AppElevatedButton(
                    width: MySize.getScaledSizeWidth(335),
                    TypoGraphy.text(buttonText,
                        color: ColorConstant.white, level: 3),
                    onPressed: () {
                      context.pushNamed(route);
                    },
                  )
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
