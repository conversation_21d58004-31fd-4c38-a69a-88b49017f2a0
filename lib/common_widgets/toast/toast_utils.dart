import 'package:bot_toast/bot_toast.dart';
import 'package:flutter/material.dart';
import 'package:khelnet/common_widgets/toast/toast_list_tile.dart';

class ToastUtils {
  static void showSuccessToast(
      {required BuildContext context, required String msg}) {
    if (msg.compareTo('') == 0) {
      showSuccess(message: "Session Expired");
    } else {
      showSuccess(message: msg);
    }
  }

  static void showFailedToast(
      {required BuildContext context, required String msg}) {
    if (msg.compareTo('') == 0) {
      showFailed(message: "Session Expired");
    } else {
      showFailed(message: msg);
    }
  }

  static void showSuccess(
          {required String message,
          int duration = 3,
          void Function()? onTap}) =>
      BotToast.showCustomNotification(
        duration: Duration(seconds: duration),
        toastBuilder: (CancelFunc? cancelFunc) => ToastListTile(
          onTap: onTap,
          iconData: Icons.check,
          textWidget: Text(
            message,
            overflow: TextOverflow.ellipsis,
            maxLines: 5,
            style: const TextStyle(color: Colors.white, fontFamily: "Poppins"),
          ),
          bgColor: Colors.green[300]!,
        ),
      );

  static void showFailed({required String message, int duration = 3}) =>
      BotToast.showCustomNotification(
        duration: Duration(seconds: duration),
        toastBuilder: (CancelFunc? cancelFunc) => ToastListTile(
          iconData: Icons.close,
          bgColor: Colors.redAccent,
          textWidget: RichText(
            text: TextSpan(text: message),
          ),
        ),
      );

  static void showBusy({required String message, int duration = 3}) =>
      BotToast.showCustomNotification(
        duration: Duration(seconds: duration),
        toastBuilder: (CancelFunc? cancelFunc) => ToastListTile(
          iconData: Icons.phone_callback_outlined,
          bgColor: Colors.red,
          textWidget: RichText(
            text: TextSpan(text: message),
          ),
        ),
      );
}
