import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:khelnet/global/constants/app_constant.dart';
import 'package:khelnet/global/constants/global_methods.dart';
import 'package:khelnet/modules/private_route/home/<USER>/home_bloc/home_bloc.dart';
import 'package:khelnet/modules/private_route/my_academy/view/widget/bottom_bar_helper.dart';
import 'package:khelnet/modules/private_route/wallet/view/wallet_screen.dart';
import 'package:khelnet/modules/testing/coming_soon_screen.dart';

import '../../../auth/delete_account_bloc/delete_account_bloc.dart';
import '../../../auth/login/controller/location_bloc/location_bloc.dart';
import '../../academy_profile/controller/get_academy_detail_bloc/get_academy_detail_bloc.dart';
import '../../academy_profile/view/academy_profile_screen.dart';
import '../../add_coach/controller/get_center_batch_bloc/get_center_batch_bloc.dart';
import '../../my_academy/view/notification_screen.dart';
import '../../wallet/controller/bank_detail_bloc/bank_detail_bloc.dart';
import '../../wallet/controller/cash_free_wallet_bloc/cash_free_wallet_bloc.dart';
import '../../wallet/controller/waltte_overview_bloc/wallet_overview_bloc.dart';
import '../controller/cash_free_bloc/cash_free_bloc.dart';
import '../controller/subscription_bloc/subscription_bloc.dart';
import 'drawer_helper.dart';
import 'home_page.dart';

class HomeScreen extends StatefulWidget {
  final bool isForCoach, isFromSetting;

  const HomeScreen(
      {super.key, required this.isForCoach, this.isFromSetting = false});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final List<Widget> coachScreen = [
    MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => HomeBloc(),
        ),
        BlocProvider(
          create: (context) => LocationBloc(),
        ),
        BlocProvider(
          create: (context) => GetCenterBatchBloc(),
        ),
        BlocProvider(
          create: (context) => CashFreeBloc(),
        ),
        BlocProvider(
          create: (context) => SubscriptionBloc(),
        ),
      ],
      child: HomePage(isForCoach: true, scaffoldKey: scaffoldKey),
    ),
    const SizedBox.shrink(),
    const ComingSoonScreen(),
    const NotificationScreen(),
    MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) => HomeBloc(),
          ),
          BlocProvider(
            create: (context) => GetAcademyDetailBloc(),
          ),
        ],
        child: const AcademyProfileScreen(
          isForCoach: true,
        ))
  ];
  static GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();

  final List<Widget> academyScreen = [
    MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => CashFreeBloc(),
        ),
        BlocProvider(
          create: (context) => SubscriptionBloc(),
        ),
        BlocProvider(
          create: (context) => HomeBloc(),
        ),
      ],
      child: HomePage(
        isForCoach: false,
        scaffoldKey: scaffoldKey,
      ),
    ),
    MultiBlocProvider(providers: [
      BlocProvider(
        create: (context) => BankDetailBloc(),
      ),
      BlocProvider(
        create: (context) => CashFreeWalletBloc(),
      ),
      BlocProvider(
        create: (context) => WalletOverviewBloc(),
      ),
    ], child: const WalletScreen()),
    const ComingSoonScreen(),
    const NotificationScreen(),
    MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) => HomeBloc(),
          ),
          BlocProvider(
            create: (context) => GetAcademyDetailBloc(),
          ),
        ],
        child: const AcademyProfileScreen(
          isForCoach: false,
        ))
    // const ProfileScreen(),
  ];
  final ValueNotifier<int> _currentIndex = ValueNotifier(0);

  void _onItemTapped(int index) {
    _currentIndex.value = index;
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).popUntil((route) => route.isFirst);
    }
  }

  bool _handleBackPress() {
    if (widget.isFromSetting &&
        _currentIndex.value == 1 &&
        Navigator.of(context).canPop()) {
      Navigator.of(context).pop();
      return false;
    }
    if (_currentIndex.value == 0 && Navigator.of(context).canPop()) {
      return true;
    }
    return false;
  }

  @override
  void initState() {
    GlobalMethods.getFcmToken();
    if (widget.isFromSetting) {
      _currentIndex.value = 1;
    }
    AppConstant.isHomeScreen = true;
    // widget.isForCoach
    //     ? context.read<HomeBloc>().add(GetCoachHomeData())
    //     : context.read<HomeBloc>().add(GetAcademyHomeData());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: _currentIndex,
      builder: (context, value, child) => PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) {
          if (!didPop) {
            if (_handleBackPress()) {
              Navigator.of(context).pop();
            }
          }
        },
        child: Scaffold(
          endDrawer: MultiBlocProvider(
            providers: [
              BlocProvider(
                create: (context) => HomeBloc(),
              ),
              BlocProvider(
                create: (context) => DeleteAccountBloc(),
              )
            ],
            child: DrawerHelper(
              scaffoldKey: scaffoldKey,
              isForCoach: widget.isForCoach,
            ),
          ),
          key: scaffoldKey,
          body: widget.isForCoach
              ? coachScreen[_currentIndex.value]
              : academyScreen[_currentIndex.value],
          bottomNavigationBar: ValueListenableBuilder(
            valueListenable: AppConstant.academyLogo,
            builder: (context, value, child) => BottomBarHelper(
              index: _currentIndex.value,
              onItemTapped: _onItemTapped,
              isForCoach: widget.isForCoach,
            ),
          ),
        ),
      ),
    );
  }
}
