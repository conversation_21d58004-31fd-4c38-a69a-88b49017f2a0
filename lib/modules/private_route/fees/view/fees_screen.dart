import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';
import 'package:khelnet/common_widgets/custom_app_bar.dart';
import 'package:khelnet/common_widgets/failure_widget.dart';
import 'package:khelnet/common_widgets/loader_helper.dart';
import 'package:khelnet/global/constants/app_constant.dart';
import 'package:khelnet/global/constants/gradient_text.dart';
import 'package:khelnet/global/constants/size.dart';
import 'package:khelnet/modules/private_route/fees/controller/availablity_bloc/availability_state.dart';
import 'package:khelnet/modules/private_route/fees/view/widget/fees_std_card_helper.dart';
import 'package:khelnet/modules/private_route/fees/view/widget/upper_container_helper.dart';
import 'package:khelnet/utils/constants/color_constant.dart';
import 'package:khelnet/utils/constants/routes_constant.dart';
import 'package:khelnet/utils/extension/navigation_extension.dart';
import 'package:khelnet/utils/manager/storage_manager.dart';
import 'package:khelnet/utils/theme/typography.dart';
import 'package:mixpanel_flutter/mixpanel_flutter.dart';
import 'package:wolt_modal_sheet/wolt_modal_sheet.dart';

import '../../../../common_widgets/custom_svg_picture.dart';
import '../../../../common_widgets/multiple_selection_bottom_sheet.dart';
import '../../../../common_widgets/search_field.dart';
import '../../../../common_widgets/single_selection_bottom_sheet.dart';
import '../../../../services/mixpanel_manager.dart';
import '../../../../services/util_methods.dart';
import '../../../../utils/constants/app_asset.dart';
import '../../add_coach/controller/get_center_batch_bloc/get_center_batch_bloc.dart';
import '../../add_coach/controller/get_center_batch_bloc/get_center_batch_event.dart';
import '../../add_coach/controller/get_center_batch_bloc/get_center_batch_state.dart';
import '../../add_coach/model/get_batches_model.dart';
import '../../add_coach/model/get_center_model.dart';
import '../../add_student/controller/get_data_bloc/get_charges_plan_bloc.dart';
import '../../add_student/controller/get_data_bloc/get_charges_plan_event.dart';
import '../../add_student/controller/get_data_bloc/get_charges_plan_state.dart';
import '../../my_academy/view/plan_and_charge/model/charge_info_model.dart';
import '../controller/availablity_bloc/availability_bloc.dart';
import '../controller/availablity_bloc/availability_event.dart';
import '../controller/get_students_for_fees/get_students_for_fees_bloc.dart';
import '../controller/get_students_for_fees/get_students_for_fees_event.dart';
import '../controller/get_students_for_fees/get_students_for_fees_state.dart';

class FeesScreen extends StatefulWidget {
  const FeesScreen({super.key});

  @override
  State<FeesScreen> createState() => _FeesScreenState();
}

class _FeesScreenState extends State<FeesScreen> with TickerProviderStateMixin {
  TabController? _tabController;

  final ValueNotifier<bool> _isFilterApplied = ValueNotifier(false),
      isPastDuesVisible = ValueNotifier(false),
      isInstallmentVisible = ValueNotifier(false);
  final ValueNotifier<int> _page = ValueNotifier(1),
      totalAmount = ValueNotifier(0),
      selectedTab = ValueNotifier(0);
  final ValueNotifier<bool> isSelectMultiple = ValueNotifier(false);
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  List<ChargeDetails> assignCharge = [], selectedCharge = [];
  final List<ValueNotifier<bool>> isChargeSelected =
      List.generate(0, (_) => ValueNotifier<bool>(false));
  ValueNotifier<bool> gst = ValueNotifier(false);
  List<ValueNotifier<bool>> activeStudentList = [], finalActiveStudentList = [];
  ValueNotifier<double> height = ValueNotifier(173);
  List<Batches> assignBatches = [];
  List<Centers> assignCenters = [];
  List<String> selectedSport = [], selectedType = [];
  List<int> selectedBatches = [], selectedCenters = [];
  List<ValueNotifier<bool>> isCenterSelected =
          List.generate(0, (_) => ValueNotifier<bool>(false)),
      isBatchSelected = List.generate(0, (_) => ValueNotifier<bool>(false)),
      isPaymentSelected = List.generate(2, (_) => ValueNotifier<bool>(false)),
      isSportSelected = List.generate(0, (_) => ValueNotifier<bool>(false));

  void loadStudents(
      {bool shouldReload = true,
      bool isForSearch = false,
      String centerIds = '',
      String sports = '',
      String batchIds = '',
      String type = ''}) {
    context.read<GetStudentsForFeesBloc>().add(GetStudents(
        searchString: _searchController.text,
        page: _page.value,
        isForSearch: isForSearch,
        type: type,
        shouldReload: shouldReload,
        centerIds: centerIds,
        sports: sports,
        batchIds: batchIds,
        coachIds: '',
        studentWithInstallment: 0,
        startDate: DateFormat('yyyy-MM-dd')
            .format(DateTime(DateTime.now().year, DateTime.now().month, 1)),
        endDate: DateFormat('yyyy-MM-dd').format(
          DateTime(DateTime.now().year, DateTime.now().month + 1, 0),
        )));
  }

  late final Mixpanel mixpanel;

  Future<void> initMixpanel() async {
    mixpanel = await MixpanelManager.init();
  }

  getData() async {
    bool? hasGst =
        await StorageManager().getBoolData(AppConstant.storageConstant.hasGst);
    List<String>? sportList = await StorageManager.instance
        .getList(AppConstant.storageConstant.sports);
    if (sportList != null) {
      isSportSelected.addAll(
        List.generate(sportList.length, (_) => ValueNotifier<bool>(false)),
      );
    }
    if (hasGst != null) {
      gst.value = hasGst;
    }
  }

  @override
  void initState() {
    getData();
    loadStudents();
    initMixpanel();
    _tabController = TabController(length: 2, vsync: this, initialIndex: 0);
    context.read<GetCenterBatchBloc>().add(GetCenters());
    context.read<AvailabilityBloc>().add(GetModuleAvailability());
    context.read<GetChargesPlanBloc>().add(GetAllCharges());
    _scrollController.addListener(
      () => UtilMethods().onScrollListener(() {
        _page.value++;
        loadStudents(
            shouldReload: false,
            centerIds: selectedCenters.join(','),
            sports: selectedSport.join(','),
            type: selectedType.join(','),
            batchIds: selectedBatches.join(','));
      }, _scrollController),
    );

    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _scrollController.removeListener(
      () => UtilMethods().onScrollListener(() {
        _page.value++;
        loadStudents(shouldReload: false);
      }, _scrollController),
    );
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        "Fees",
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 10.0),
            child: GestureDetector(
              onTap: () {
                context.pushNamed(RouteConstant.feesSettingsScreen);
              },
              child: const Icon(
                Icons.settings,
                color: ColorConstant.black,
              ),
            ),
          )
        ],
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<GetStudentsForFeesBloc, GetStudentsForFeesState>(
            listener: (BuildContext context, GetStudentsForFeesState state) {
              if (state is GetStudentsForFeesSuccess) {
                totalAmount.value = state.totalAmount;
              }
            },
          ),
          BlocListener<GetChargesPlanBloc, GetChargePlanState>(
            listener: (context, state) {
              if (state is GetChargeSuccess) {
                assignCharge = state.allChargesModel.chargeData.charges;
                isChargeSelected.addAll(List.generate(
                    state.allChargesModel.chargeData.charges.length,
                    (_) => ValueNotifier<bool>(false)));
              }
            },
          ),
          BlocListener<GetCenterBatchBloc, GetCenterBatchState>(
              listener: (BuildContext context, GetCenterBatchState state) {
            if (state is GetCenterSuccess) {
              assignCenters = state.getCenterModel.centers;
              isCenterSelected.addAll(
                List.generate(state.getCenterModel.centers.length,
                    (_) => ValueNotifier<bool>(false)),
              );
            } else if (state is GetBatchSuccess) {
              assignBatches = state.getBatchesModel.batchData.batches;
              isBatchSelected.addAll(
                List.generate(state.getBatchesModel.batchData.batches.length,
                    (_) => ValueNotifier<bool>(false)),
              );
            }
          }),
          BlocListener<GetStudentsForFeesBloc, GetStudentsForFeesState>(
            listener: (BuildContext context, GetStudentsForFeesState state) {
              if (state is GetStudentsForFeesSuccess) {
                activeStudentList = List.generate(state.studentsForFees.length,
                    (index) => ValueNotifier<bool>(false));
                finalActiveStudentList.addAll(activeStudentList);
              }
            },
          ),
          BlocListener<AvailabilityBloc, AvailabilityState>(
            listener: (context, state) {
              if (state is AvailabilitySuccess) {
                isInstallmentVisible.value =
                    state.availabilityModel.data.hasInstallment;
                isPastDuesVisible.value = state.availabilityModel.data.pastDue;
              }
            },
          )
        ],
        child: Padding(
          padding: EdgeInsets.only(top: MySize.getScaledSizeHeight(10)),
          child: Column(
            children: [
              Gap(MySize.getScaledSizeHeight(15)),
              GestureDetector(
                onTap: () {
                  // context.pushNamed("");
                },
                child: ValueListenableBuilder(
                  valueListenable: height,
                  builder: (context, value, child) => AnimatedContainer(
                    curve: Curves.fastOutSlowIn,
                    height: MySize.getScaledSizeHeight(value),
                    duration: const Duration(milliseconds: 300),
                    child: Container(
                      width: MySize.getScaledSizeWidth(357),
                      height: MySize.getScaledSizeHeight(173),
                      decoration: const BoxDecoration(
                          image: DecorationImage(
                              fit: BoxFit.fill,
                              image: AssetImage(AppAsset.feesContainer))),
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: MySize.getScaledSizeWidth(25),
                            vertical: MySize.getScaledSizeHeight(10)),
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              TypoGraphy.text("Total Money Pending",
                                  fontSize: MySize.getScaledSizeHeight(12),
                                  color: ColorConstant.white,
                                  fontWeight: FontWeight.w500),
                              ValueListenableBuilder(
                                valueListenable: totalAmount,
                                builder: (context, value, child) => Row(
                                  children: [
                                    CustomSvgPicture(
                                      AppAsset.rupeeSvg,
                                      color: ColorConstant.white,
                                      height: MySize.getScaledSizeHeight(16),
                                    ),
                                    Gap(MySize.getScaledSizeWidth(3)),
                                    TypoGraphy.text("${totalAmount.value}",
                                        fontSize:
                                            MySize.getScaledSizeHeight(20),
                                        color: ColorConstant.white,
                                        fontWeight: FontWeight.w600),
                                  ],
                                ),
                              ),
                              // TypoGraphy.text("Today",
                              //     fontSize: MySize.getScaledSizeHeight(10),
                              //     color: ColorConstant.white,
                              //     fontWeight: FontWeight.w500),
                              // Row(
                              //   children: [
                              //     CustomSvgPicture(
                              //       AppAsset.rupeeSvg,
                              //       color: ColorConstant.white,
                              //       height: MySize.getScaledSizeHeight(16),
                              //     ),
                              //     Gap(MySize.getScaledSizeWidth(3)),
                              //     TypoGraphy.text("--",
                              //         color: ColorConstant.white,
                              //         level: 1,
                              //         fontWeight: FontWeight.w500),
                              //   ],
                              // ),
                              Gap(MySize.getScaledSizeHeight(80)),
                              InkWell(
                                onTap: () {
                                  context.pushNamed(
                                      RouteConstant.feeDashBoardScreen);
                                },
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const CustomSvgPicture(
                                        AppAsset.dashBoardSvg),
                                    Gap(MySize.getScaledSizeWidth(10)),
                                    TypoGraphy.text("View Dashboard",
                                        color: ColorConstant.white,
                                        fontWeight: FontWeight.w500,
                                        fontSize:
                                            MySize.getScaledSizeHeight(10)),
                                    const Expanded(child: SizedBox.shrink()),
                                    const CustomSvgPicture(AppAsset.rightArrow)
                                  ],
                                ),
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              Gap(MySize.getScaledSizeHeight(15)),
              SizedBox(
                height: MySize.getScaledSizeHeight(70),
                child: ListView(
                  scrollDirection: Axis.horizontal,
                  children: [
                    Gap(MySize.getScaledSizeWidth(12)),
                    ValueListenableBuilder(
                      valueListenable: isPastDuesVisible,
                      builder: (context, value, child) => value
                          ? UpperContainerHelper(
                              isPastDues: true,
                              color: ColorConstant.absentDarkColor,
                              image: AppAsset.pastDues,
                              title: "Past Dues",
                              onTap: () {
                                mixpanel.track("Past Dues Button", properties: {
                                  "Academy Name": AppConstant.academyName
                                });
                                context.pushNamed(RouteConstant.pastDuesScreen);
                              },
                            )
                          : const SizedBox.shrink(),
                    ),
                    Gap(MySize.getScaledSizeWidth(12)),
                    UpperContainerHelper(
                      image: AppAsset.upcoming,
                      title: "Upcoming",
                      onTap: () {
                        mixpanel.track("Upcoming Button", properties: {
                          "Academy Name": AppConstant.academyName
                        });
                        context.pushNamed(RouteConstant.upcomingDuesScreen);
                      },
                    ),
                    Gap(MySize.getScaledSizeWidth(12)),
                    UpperContainerHelper(
                      image: AppAsset.tileLeadingIcon,
                      title: "Charges",
                      onTap: () {
                        mixpanel.track("Charges Button", properties: {
                          "Academy Name": AppConstant.academyName
                        });
                        context.pushNamed(RouteConstant.allStudentScreen,
                            args: {"isForCharges": true});
                      },
                    ),
                    Gap(MySize.getScaledSizeWidth(12)),
                    UpperContainerHelper(
                      color: const Color(0XFF108B24),
                      isPastDues: true,
                      image: AppAsset.historyFees,
                      title: "History",
                      onTap: () {
                        mixpanel.track("History Button", properties: {
                          "Academy Name": AppConstant.academyName
                        });
                        context.pushNamed(RouteConstant.historyScreen);
                      },
                    ),
                    Gap(MySize.getScaledSizeWidth(12)),
                    ValueListenableBuilder(
                      valueListenable: isInstallmentVisible,
                      builder: (context, value, child) => value
                          ? UpperContainerHelper(
                              image: AppAsset.installmentFees,
                              title: "Installments",
                              onTap: () {
                                mixpanel.track("Installment Button",
                                    properties: {
                                      "Academy Name": AppConstant.academyName
                                    });
                                context.pushNamed(RouteConstant.pastDuesScreen,
                                    args: {"isForInstallments": true});
                              },
                            )
                          : const SizedBox.shrink(),
                    ),
                    Gap(MySize.getScaledSizeWidth(12)),
                  ],
                ),
              ),
              Gap(MySize.getScaledSizeHeight(10)),
              const Divider(
                thickness: 0.5,
                color: ColorConstant.gray,
              ),
              Gap(MySize.getScaledSizeHeight(6)),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const SizedBox.shrink(),
                  Padding(
                    padding:
                        EdgeInsets.only(left: MySize.getScaledSizeWidth(120.0)),
                    child: Column(
                      children: [
                        Container(
                          width: MySize.getScaledSizeWidth(27),
                          height: MySize.getScaledSizeHeight(3),
                          decoration: const BoxDecoration(
                              color: ColorConstant.textFieldColor,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(23))),
                        ),
                        Gap(MySize.getScaledSizeHeight(3)),
                        Container(
                          width: MySize.getScaledSizeWidth(19),
                          height: MySize.getScaledSizeHeight(3),
                          decoration: const BoxDecoration(
                              color: ColorConstant.textFieldColor,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(23))),
                        ),
                      ],
                    ),
                  ),
                  ValueListenableBuilder(
                    valueListenable: isSelectMultiple,
                    builder: (context, value, child) => GestureDetector(
                        onTap: () {
                          isSelectMultiple.value = true;
                          context
                              .pushNamed(
                                  RouteConstant.multipleSelectStudentScreen)
                              .then(
                            (value) {
                              _page.value = 1;
                              isSelectMultiple.value = false;
                              loadStudents(isForSearch: true);
                            },
                          );
                        },
                        child: Padding(
                          padding: const EdgeInsets.only(right: 10.0),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              GradientText(
                                gradient: const LinearGradient(colors: [
                                  ColorConstant.white,
                                  ColorConstant.primaryColor
                                ]),
                                child: TypoGraphy.text("Select Multiple",
                                    fontWeight: FontWeight.w500,
                                    color: ColorConstant.primaryColor,
                                    fontSize: MySize.getScaledSizeHeight(15)),
                              ),
                              Gap(MySize.getScaledSizeWidth(6)),
                              CustomSvgPicture(
                                AppAsset.multipleSelectionStudent,
                                height: MySize.getScaledSizeWidth(15),
                                color: ColorConstant.signUp,
                              )
                            ],
                          ),
                        )),
                  ),
                ],
              ),
              Gap(MySize.getScaledSizeHeight(20)),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10.0),
                child: Row(
                  children: [
                    Expanded(
                      child: SearchField(
                        onClearTap: () {
                          _page.value = 1;
                          loadStudents(
                              shouldReload: false,
                              isForSearch: true,
                              centerIds: selectedCenters.join(','),
                              sports: selectedSport.join(','),
                              type: selectedType.join(','),
                              batchIds: selectedBatches.join(','));
                        },
                        controller: _searchController,
                        onChanged: (val) {
                          _page.value = 1;
                          loadStudents(
                              shouldReload: false,
                              isForSearch: true,
                              centerIds: selectedCenters.join(','),
                              sports: selectedSport.join(','),
                              type: selectedType.join(','),
                              batchIds: selectedBatches.join(','));
                        },
                      ),
                    ),
                    Gap(MySize.getScaledSizeWidth(8)),
                    GestureDetector(
                      onTap: () {
                        if (selectedCenters.isEmpty) {
                          for (int i = 0; i < isCenterSelected.length; i++) {
                            isCenterSelected[i].value = false;
                          }
                        }
                        if (selectedBatches.isEmpty) {
                          for (int i = 0; i < isBatchSelected.length; i++) {
                            isBatchSelected[i].value = false;
                          }
                        }
                        if (selectedSport.isEmpty) {
                          for (int i = 0; i < isSportSelected.length; i++) {
                            isSportSelected[i].value = false;
                          }
                        }
                        if (selectedType.isEmpty) {
                          for (int i = 0; i < isPaymentSelected.length; i++) {
                            isPaymentSelected[i].value = false;
                          }
                        }
                        WoltModalSheet.show(
                          context: context,
                          pageListBuilder: (modalSheetContext) {
                            return [
                              MultipleSelectionBottomSheet.filterBottomSheet(
                                isForMain: true,
                                context,
                                isAllTimeSelected: ValueNotifier(false),
                                isLastMonthSelected: ValueNotifier(false),
                                isPaymentStatusAvailable: true,
                                centerData: List.generate(
                                    assignCenters.length,
                                    (index) => Centers(
                                          id: assignCenters[index].id,
                                          name: assignCenters[index].name,
                                        )),
                                batchData: ValueNotifier(List.generate(
                                    assignBatches.length,
                                    (index) => Batches(
                                          id: assignBatches[index].id,
                                          name: assignBatches[index].name,
                                          startTime: '',
                                          endTime: '',
                                        ))),
                                isCenterSelected: isCenterSelected,
                                isBatchSelected: isBatchSelected,
                                onApply: (List<int> selectedCenterIds,
                                    List<int> selectedBatchIds,
                                    List<String> sports,
                                    List<String> payment,
                                    String startDate,
                                    String endDate,
                                    ValueNotifier<bool> isFilterApplied) {
                                  selectedCenters = selectedCenterIds;
                                  selectedBatches = selectedBatchIds;
                                  selectedType = payment;
                                  selectedSport = sports;
                                  _isFilterApplied.value =
                                      isFilterApplied.value;
                                  context
                                      .read<GetCenterBatchBloc>()
                                      .add(GetBatches(id: selectedCenterIds));
                                  _page.value = 1;
                                  loadStudents(
                                      centerIds: selectedCenterIds.join(','),
                                      batchIds: selectedBatchIds.join(','),
                                      isForSearch: true,
                                      shouldReload: true,
                                      type: selectedType.join(','),
                                      sports: selectedSport.join(','));
                                },
                                isSportSelected: isSportSelected,
                                selectedCenterIds: selectedCenters,
                                initialBatchData: assignBatches,
                                isPaymentStatusSelected: isPaymentSelected,
                              )
                            ];
                          },
                        );
                      },
                      child: CircleAvatar(
                        backgroundColor: ColorConstant.textFieldColor,
                        radius: 22,
                        child: ValueListenableBuilder(
                          valueListenable: _isFilterApplied,
                          builder: (context, value, child) => CustomSvgPicture(
                            AppAsset.filterAttendanceSvg,
                            color: value
                                ? ColorConstant.signUp
                                : ColorConstant.black,
                          ),
                        ),
                      ),
                    ),
                    Gap(MySize.getScaledSizeWidth(8)),
                    GestureDetector(
                      onTap: () {
                        context.pushNamed(RouteConstant.feesReminderScreen);
                      },
                      child: const CircleAvatar(
                        backgroundColor: ColorConstant.textFieldColor,
                        radius: 22,
                        child: CustomSvgPicture(
                          AppAsset.notificationFees,
                          color: ColorConstant.black,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Gap(MySize.getScaledSizeHeight(10)),
              ValueListenableBuilder(
                valueListenable: _isFilterApplied,
                builder: (context, value, child) => value
                    ? GestureDetector(
                        onTap: () {
                          selectedCenters.clear();
                          selectedBatches.clear();
                          selectedType.clear();
                          selectedSport.clear();
                          isCenterSelected.clear();
                          isBatchSelected.clear();
                          isSportSelected.clear();
                          isPaymentSelected.clear();
                          isCenterSelected = List.generate(
                            assignCenters.length,
                            (index) => ValueNotifier<bool>(false),
                          );
                          isPaymentSelected = List.generate(
                            2,
                            (index) => ValueNotifier<bool>(false),
                          );
                          getData();
                          isBatchSelected = List.generate(
                            assignBatches.length,
                            (index) => ValueNotifier<bool>(false),
                          );
                          _page.value = 1;
                          loadStudents(
                              shouldReload: true,
                              isForSearch: true,
                              centerIds: selectedCenters.join(','),
                              sports: selectedSport.join(','),
                              type: selectedType.join(','),
                              batchIds: selectedBatches.join(','));
                          _isFilterApplied.value = false;
                        },
                        child: Padding(
                          padding: EdgeInsets.only(
                              left: MySize.getScaledSizeWidth(8)),
                          child: Align(
                            alignment: Alignment.centerLeft,
                            child: Container(
                              decoration: const BoxDecoration(
                                  color: ColorConstant.gradient1,
                                  gradient: LinearGradient(colors: [
                                    ColorConstant.gradient1,
                                    ColorConstant.gradient2,
                                  ]),
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(34))),
                              height: MySize.getScaledSizeHeight(35),
                              width: MySize.getScaledSizeWidth(110),
                              child: Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: MySize.getScaledSizeWidth(12)),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    TypoGraphy.text("Reset Filter",
                                        color: ColorConstant.white, level: 1),
                                    CustomSvgPicture(
                                      AppAsset.crossIcon,
                                      color: ColorConstant.white,
                                      height: MySize.getScaledSizeHeight(10),
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      )
                    : const SizedBox.shrink(),
              ),
              Gap(MySize.getScaledSizeHeight(10)),
              BlocBuilder<GetStudentsForFeesBloc, GetStudentsForFeesState>(
                builder: (BuildContext context, GetStudentsForFeesState state) {
                  if (state is GetStudentsForFeesSuccess) {
                    return state.studentsForFees.isNotEmpty
                        ? Expanded(
                            child: Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal: MySize.getScaledSizeWidth(10),
                                vertical: MySize.getScaledSizeHeight(8)),
                            child: NotificationListener<UserScrollNotification>(
                              onNotification: (notification) {
                                final ScrollDirection direction =
                                    notification.direction;
                                if (direction == ScrollDirection.reverse) {
                                  height.value = 0;
                                } else if (direction ==
                                    ScrollDirection.forward) {
                                  height.value = 173;
                                }
                                return true;
                              },
                              child: ListView.separated(
                                  controller: _scrollController,
                                  itemBuilder: (context, index) {
                                    int amount = 0;
                                    DateTime dueDate = DateTime.now();
                                    DateTime planCreatedDate = DateTime.now();
                                    String formattedDate = '';
                                    if (index < state.studentsForFees.length) {
                                      formattedDate = DateFormat('yyyy-MM-dd')
                                          .format(DateTime.parse(state
                                              .studentsForFees[index]
                                              .studentPlans[0]
                                              .createdAt));
                                      planCreatedDate = DateFormat('yyyy-MM-dd')
                                          .parse(formattedDate);
                                      dueDate = DateTime.parse(state
                                          .studentsForFees[index]
                                          .studentPlans[0]
                                          .dueDate);
                                      amount = state
                                                  .studentsForFees[index]
                                                  .studentPlans[0]
                                                  .paymentInstallment !=
                                              null
                                          ? state
                                                      .studentsForFees[index]
                                                      .studentPlans[0]
                                                      .paymentInstallment!
                                                      .advance ==
                                                  0
                                              ? state
                                                  .studentsForFees[index]
                                                  .studentPlans[0]
                                                  .paymentInstallment!
                                                  .installment[0]
                                              : state
                                                  .studentsForFees[index]
                                                  .studentPlans[0]
                                                  .paymentInstallment!
                                                  .advance
                                          : state.studentsForFees[index]
                                              .studentPlans[0].amount;
                                    }
                                    // String planType = state
                                    //             .studentsForFees[index]
                                    //             .studentPlans[0]
                                    //             .paymentInstallment !=
                                    //         null
                                    //     ? state
                                    //                 .studentsForFees[index]
                                    //                 .studentPlans[0]
                                    //                 .paymentInstallment!
                                    //                 .advance ==
                                    //             0
                                    //         ? "Installment"
                                    //         : "Advance"
                                    //     : "";
                                    return index >=
                                                state.studentsForFees.length &&
                                            !state.hasReachedMax
                                        ? const LoaderHelper(
                                            height: 30,
                                          )
                                        : FeesStdCardHelper(
                                            profileImage: File(state
                                                .studentsForFees[index]
                                                .profilePic),
                                            isPlanExpired: ValueNotifier(
                                                DateTime.now().isAfter(
                                                    DateTime.parse(state
                                                        .studentsForFees[index]
                                                        .studentPlans[0]
                                                        .dueDate))),
                                            name: state
                                                .studentsForFees[index].name,
                                            batchName: state
                                                    .studentsForFees[index]
                                                    .studentAssignBatches[0]
                                                    .academyCenterBatch
                                                    ?.name ??
                                                "",
                                            endTime: state
                                                    .studentsForFees[index]
                                                    .studentAssignBatches[0]
                                                    .academyCenterBatch
                                                    ?.endTime ??
                                                "",
                                            startTime: state
                                                    .studentsForFees[index]
                                                    .studentAssignBatches[0]
                                                    .academyCenterBatch
                                                    ?.startTime ??
                                                "",
                                            dateTime: dueDate,
                                            isUnPaid: ValueNotifier(
                                                planCreatedDate.month ==
                                                        dueDate.month &&
                                                    planCreatedDate.year ==
                                                        dueDate.year),
                                            isForUpcoming: ValueNotifier(false),
                                            onStatusTap: finalActiveStudentList[
                                                        index]
                                                    .value
                                                ? () {}
                                                : () {
                                                    mixpanel.track(
                                                        planCreatedDate.month ==
                                                                    DateTime.now()
                                                                        .month &&
                                                                planCreatedDate.year == DateTime.now().year
                                                            ? "Unpaid Button"
                                                            : "Renew Button",
                                                        properties: {
                                                          "Academy Name":
                                                              AppConstant
                                                                  .academyName
                                                        });
                                                    DateTime dueDate =
                                                        DateTime.parse(state
                                                            .studentsForFees[
                                                                index]
                                                            .studentPlans[0]
                                                            .dueDate);
                                                    int additionalMonths = state
                                                                .studentsForFees[
                                                                    index]
                                                                .studentPlans[0]
                                                                .months !=
                                                            0
                                                        ? state
                                                            .studentsForFees[
                                                                index]
                                                            .studentPlans[0]
                                                            .months
                                                        : 0;
                                                    int additionalDays = state
                                                                .studentsForFees[
                                                                    index]
                                                                .studentPlans[0]
                                                                .days !=
                                                            0
                                                        ? state
                                                            .studentsForFees[
                                                                index]
                                                            .studentPlans[0]
                                                            .days
                                                        : 0;
                                                    DateTime endDate = DateTime(
                                                      dueDate.year,
                                                      dueDate.month +
                                                          additionalMonths,
                                                      dueDate.day +
                                                          additionalDays,
                                                    );
                                                    SingleSelectionBottomSheet.feePopUp(
                                                        context,
                                                        profilePic: File(state
                                                            .studentsForFees[
                                                                index]
                                                            .profilePic),
                                                        isChargeSelected:
                                                            isChargeSelected,
                                                        assignCharge:
                                                            assignCharge,
                                                        studentName: state
                                                            .studentsForFees[
                                                                index]
                                                            .name,
                                                        batchName: state
                                                                .studentsForFees[
                                                                    index]
                                                                .studentAssignBatches[
                                                                    0]
                                                                .academyCenterBatch
                                                                ?.name ??
                                                            "",
                                                        tabController:
                                                            _tabController!,
                                                        amount: ValueNotifier(
                                                            amount),
                                                        planName: state
                                                            .studentsForFees[
                                                                index]
                                                            .studentPlans[0]
                                                            .name,
                                                        isForInstallment: false,
                                                        extraCharges:
                                                            ValueNotifier(state
                                                                .studentsForFees[index]
                                                                .studentPlans[0]
                                                                .charge),
                                                        gst: gst.value ? ValueNotifier((amount * 0.18).round()) : ValueNotifier(0),
                                                        startDate: ValueNotifier(DateFormat('dd/MM/yyyy').format(dueDate)),
                                                        endDate: ValueNotifier(DateFormat("dd/MM/yyyy").format(endDate)),
                                                        installmentNumber: state.studentsForFees[index].studentPlans[0].paymentInstallment != null ? 1 : 0,
                                                        planStatus: '',
                                                        totalInstallment: state.studentsForFees[index].studentPlans[0].paymentInstallment != null ? state.studentsForFees[index].studentPlans[0].paymentInstallment!.installment.length : 0,
                                                        installmentDate: DateTime.parse(state.studentsForFees[index].studentPlans[0].createdAt),
                                                        studentId: state.studentsForFees[index].id, onTap: () {
                                                      activeStudentList[index]
                                                          .value = true;
                                                      finalActiveStudentList[
                                                              index]
                                                          .value = true;
                                                    }, transactionId: 0, selectedTab: selectedTab, hasGst: gst.value);
                                                    // SingleSelectionBottomSheet
                                                    //     .pastDuesBottomSheet(
                                                    //   context,
                                                    //   studentName: state
                                                    //       .studentsForFees[
                                                    //           index]
                                                    //       .name,
                                                    //   batchName: state
                                                    //       .studentsForFees[
                                                    //           index]
                                                    //       .studentAssignBatches[
                                                    //           0]
                                                    //       .academyCenterBatch
                                                    //       .name,
                                                    //   profilePic: File(state
                                                    //       .studentsForFees[
                                                    //           index]
                                                    //       .profilePic),
                                                    //   planName: state
                                                    //       .studentsForFees[
                                                    //           index]
                                                    //       .studentPlans[0]
                                                    //       .name,
                                                    //   amount:
                                                    //       ValueNotifier(amount),
                                                    //   gst: gst
                                                    //       ? ValueNotifier(
                                                    //           (amount * 0.18)
                                                    //               .round())
                                                    //       : ValueNotifier(0),
                                                    //   // planStatus: planType,
                                                    //   planStatus: "",
                                                    //   totalInstallment: state
                                                    //               .studentsForFees[
                                                    //                   index]
                                                    //               .studentPlans[
                                                    //                   0]
                                                    //               .paymentInstallment !=
                                                    //           null
                                                    //       ? state
                                                    //           .studentsForFees[
                                                    //               index]
                                                    //           .studentPlans[0]
                                                    //           .paymentInstallment!
                                                    //           .installment
                                                    //           .length
                                                    //       : 0,
                                                    //   isChargeSelected:
                                                    //       isChargeSelected,
                                                    //   assignCharge:
                                                    //       assignCharge,
                                                    //   studentId: state
                                                    //       .studentsForFees[
                                                    //           index]
                                                    //       .id,
                                                    //   isForInstallment: false,
                                                    //   transactionId: 0,
                                                    //   installmentDate:
                                                    //       DateTime.parse(state
                                                    //           .studentsForFees[
                                                    //               index]
                                                    //           .studentPlans[0]
                                                    //           .createdAt),
                                                    //   onTap: () {
                                                    //     activeStudentList[index]
                                                    //         .value = true;
                                                    //     finalActiveStudentList[
                                                    //             index]
                                                    //         .value = true;
                                                    //   },
                                                    //   startDate: DateFormat(
                                                    //           'dd/MM/yyyy')
                                                    //       .format(dueDate),
                                                    //   endDate: DateFormat(
                                                    //           "dd/MM/yyyy")
                                                    //       .format(endDate),
                                                    //   installmentNumber: state
                                                    //               .studentsForFees[
                                                    //                   index]
                                                    //               .studentPlans[
                                                    //                   0]
                                                    //               .paymentInstallment !=
                                                    //           null
                                                    //       ? 1
                                                    //       : 0,
                                                    //   extraCharges:
                                                    //       ValueNotifier(state
                                                    //           .studentsForFees[
                                                    //               index]
                                                    //           .studentPlans[0]
                                                    //           .charge),
                                                    // );
                                                  },
                                            onTap: () {
                                              context.pushNamed(
                                                  RouteConstant
                                                      .userProfileScreen,
                                                  args: {
                                                    "stdId": state
                                                        .studentsForFees[index]
                                                        .id,
                                                    "profileImage": File(state
                                                        .studentsForFees[index]
                                                        .profilePic),
                                                    "studentName": state
                                                        .studentsForFees[index]
                                                        .name,
                                                    "batchName": state
                                                            .studentsForFees[
                                                                index]
                                                            .studentAssignBatches[
                                                                0]
                                                            .academyCenterBatch
                                                            ?.name ??
                                                        "",
                                                    "startTime": state
                                                            .studentsForFees[
                                                                index]
                                                            .studentAssignBatches[
                                                                0]
                                                            .academyCenterBatch
                                                            ?.startTime ??
                                                        "",
                                                    "endTime": state
                                                            .studentsForFees[
                                                                index]
                                                            .studentAssignBatches[
                                                                0]
                                                            .academyCenterBatch
                                                            ?.endTime ??
                                                        "",
                                                  });
                                            },
                                            isPaymentDone:
                                                finalActiveStudentList[index],
                                            isRenewDone:
                                                finalActiveStudentList[index],
                                          );
                                  },
                                  separatorBuilder: (context, index) =>
                                      Gap(MySize.getScaledSizeHeight(10)),
                                  itemCount: state.hasReachedMax
                                      ? state.studentsForFees.length
                                      : state.studentsForFees.length + 1),
                            ),
                          ))
                        : const CustomSvgPicture(AppAsset.noStudentsForFees);
                  } else if (state is GetStudentsForFeesFailure) {
                    return FailureWidget(msg: state.msg);
                  }
                  return const LoaderHelper();
                },
              )
            ],
          ),
        ),
      ),
    );
  }
}
