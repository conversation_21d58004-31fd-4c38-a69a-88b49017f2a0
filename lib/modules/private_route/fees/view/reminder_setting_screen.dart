import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:khelnet/common_widgets/buttons/app_elevated_button.dart';
import 'package:khelnet/common_widgets/custom_app_bar.dart';
import 'package:khelnet/common_widgets/custom_svg_picture.dart';
import 'package:khelnet/common_widgets/dialog/loader/custom_dialog.dart';
import 'package:khelnet/common_widgets/outlined_text_form_field.dart';
import 'package:khelnet/common_widgets/toast/toast_utils.dart';
import 'package:khelnet/global/constants/gradient_text.dart';
import 'package:khelnet/global/constants/size.dart';
import 'package:khelnet/utils/constants/app_asset.dart';
import 'package:khelnet/utils/constants/color_constant.dart';
import 'package:khelnet/utils/constants/routes_constant.dart';
import 'package:khelnet/utils/extension/navigation_extension.dart';
import 'package:khelnet/utils/theme/typography.dart';

import '../../../../global/constants/app_constant.dart';
import '../../../../utils/manager/storage_manager.dart';
import '../controller/fee_setting/reminder_setting/reminder_setting_bloc.dart';
import '../controller/fee_setting/reminder_setting/reminder_setting_event.dart';
import '../controller/fee_setting/reminder_setting/reminder_setting_state.dart';

class ReminderSettingScreen extends StatefulWidget {
  const ReminderSettingScreen({super.key});

  @override
  State<ReminderSettingScreen> createState() => _ReminderSettingScreenState();
}

class _ReminderSettingScreenState extends State<ReminderSettingScreen> {
  TextEditingController upiIdController = TextEditingController();
  final ValueNotifier<bool> isWithLinkSelected = ValueNotifier(false),
      isWithoutLinkSelected = ValueNotifier(false),
      isCoach = ValueNotifier(false),
      hasData = ValueNotifier(false);

  getData() async {
    bool? isCoachLogin = await StorageManager()
        .getBoolData(AppConstant.storageConstant.isCoachLogin);
    isCoach.value = isCoachLogin!;
  }

  @override
  void initState() {
    getData();
    context.read<ReminderSettingBloc>().add(GetReminderSetting());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar("Reminder Settings"),
      body: MultiBlocListener(
        listeners: [
          BlocListener<ReminderSettingBloc, ReminderSettingState>(
            listener: (BuildContext context, ReminderSettingState state) {
              if (state is UpdateReminderSuccess) {
                CustomDialog.hideLoader(context);
                ToastUtils.showSuccess(message: state.msg);
              } else if (state is UpdateReminderFailure) {
                CustomDialog.hideLoader(context);
                ToastUtils.showFailed(message: state.msg);
              } else if (state is UpdateReminderLoading) {
                CustomDialog.showLoader(context);
              }
            },
          ),
          BlocListener<ReminderSettingBloc, ReminderSettingState>(
            listener: (context, state) {
              if (state is GetReminderSettingSuccess) {
                CustomDialog.hideLoader(context);
                if(state.getReminderSettingModel.data != null) {
                  hasData.value = true;
                }
                upiIdController.text = state.getReminderSettingModel.data!.upi;
                isWithLinkSelected.value =
                    state.getReminderSettingModel.data!.isReminderWithUpi;
                isWithoutLinkSelected.value =
                    !state.getReminderSettingModel.data!.isReminderWithUpi;
              }
            },
          )
        ],
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Gap(MySize.getScaledSizeHeight(45)),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 5.0),
                child: TypoGraphy.text("Account Details", level: 1),
              ),
              Gap(MySize.getScaledSizeHeight(10)),
              OutlinedTextFormField(
                controller: upiIdController,
                isOnboard: true,
                prefix: const CustomSvgPicture(AppAsset.upiId),
                horizontalPadding: 0,
                hintText: "Enter Your UPI ID",
              ),
              Gap(MySize.getScaledSizeHeight(40)),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 5.0),
                child: TypoGraphy.text("Fees Reminder", level: 1),
              ),
              Gap(MySize.getScaledSizeHeight(20)),
              GestureDetector(
                onTap: () {
                  context.pushNamed(RouteConstant.homeScreen,
                      args: {
                        "isFromSetting": true,
                        "isForCoach": isCoach.value
                      });
                },
                child: Container(
                  decoration: BoxDecoration(
                      border: Border.all(color: ColorConstant.textFieldColor),
                      borderRadius: const BorderRadius.all(Radius.circular(21)),
                      color: ColorConstant.dashBoardTextField),
                  height: MySize.getScaledSizeHeight(72),
                  width: MySize.getScaledSizeWidth(357),
                  child: Padding(
                    padding: const EdgeInsets.only(left: 10.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SizedBox(
                          width: MySize.getScaledSizeWidth(200),
                          child: TypoGraphy.text(
                              "Link your payment gateway and send fee reminders",
                              level: 1,
                              textAlign: TextAlign.start,
                              maxLines: 2,
                              fontWeight: FontWeight.w500),
                        ),
                        GradientText(
                            gradient: const LinearGradient(colors: [
                              ColorConstant.white,
                              ColorConstant.primaryColor,
                            ]),
                            child: Padding(
                              padding: EdgeInsets.only(
                                  right: MySize.getScaledSizeWidth(16)),
                              child: const Icon(
                                Icons.arrow_forward_ios_rounded,
                                color: ColorConstant.primaryColor,
                              ),
                            ))
                      ],
                    ),
                  ),
                ),
              ),
              Gap(MySize.getScaledSizeHeight(24)),
              GestureDetector(
                onTap: () {
                  if (upiIdController.text.isNotEmpty) {
                    isWithLinkSelected.value = true;
                    if (isWithoutLinkSelected.value) {
                      isWithoutLinkSelected.value = false;
                    }
                  } else {
                    ToastUtils.showFailed(message: "Please Enter UPI id");
                  }
                },
                child: Container(
                  decoration: BoxDecoration(
                      border: Border.all(color: ColorConstant.textFieldColor),
                      borderRadius: const BorderRadius.all(Radius.circular(21)),
                      color: ColorConstant.dashBoardTextField),
                  height: MySize.getScaledSizeHeight(72),
                  width: MySize.getScaledSizeWidth(357),
                  child: Padding(
                    padding: const EdgeInsets.only(left: 10.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SizedBox(
                          width: MySize.getScaledSizeWidth(175),
                          child: TypoGraphy.text(
                              "Send Fee Reminder With UPI Payment Link",
                              level: 1,
                              textAlign: TextAlign.start,
                              maxLines: 2,
                              fontWeight: FontWeight.w500),
                        ),
                        ValueListenableBuilder(
                          valueListenable: isWithLinkSelected,
                          builder: (context, value, child) => Container(
                            height: MySize.getScaledSizeHeight(25),
                            width: MySize.getScaledSizeWidth(60),
                            decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border:
                                    Border.all(color: ColorConstant.signUp)),
                            child: value
                                ? Padding(
                                    padding: const EdgeInsets.all(2.5),
                                    child: Container(
                                      height: MySize.getScaledSizeHeight(20),
                                      width: MySize.getScaledSizeWidth(20),
                                      decoration: BoxDecoration(
                                          gradient:
                                              const LinearGradient(colors: [
                                            ColorConstant.gradient1,
                                            ColorConstant.gradient2,
                                          ]),
                                          color: ColorConstant.primaryColor,
                                          shape: BoxShape.circle,
                                          border: Border.all(
                                              color: ColorConstant.white)),
                                    ),
                                  )
                                : const SizedBox.shrink(),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Gap(MySize.getScaledSizeHeight(24)),
              GestureDetector(
                onTap: () {
                  isWithoutLinkSelected.value = true;
                  if (isWithLinkSelected.value) {
                    isWithLinkSelected.value = false;
                  }
                },
                child: Container(
                  decoration: BoxDecoration(
                      border: Border.all(color: ColorConstant.textFieldColor),
                      borderRadius: const BorderRadius.all(Radius.circular(21)),
                      color: ColorConstant.dashBoardTextField),
                  height: MySize.getScaledSizeHeight(72),
                  width: MySize.getScaledSizeWidth(357),
                  child: Padding(
                    padding: const EdgeInsets.only(left: 10.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SizedBox(
                          width: MySize.getScaledSizeWidth(175),
                          child: TypoGraphy.text(
                              "Send Fee Reminder Without UPI Payment Link",
                              level: 1,
                              textAlign: TextAlign.start,
                              maxLines: 2,
                              fontWeight: FontWeight.w500),
                        ),
                        ValueListenableBuilder(
                          valueListenable: isWithoutLinkSelected,
                          builder: (context, value, child) => Container(
                            height: MySize.getScaledSizeHeight(25),
                            width: MySize.getScaledSizeWidth(60),
                            decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border:
                                    Border.all(color: ColorConstant.signUp)),
                            child: value
                                ? Padding(
                                    padding: const EdgeInsets.all(2.5),
                                    child: Container(
                                      height: MySize.getScaledSizeHeight(20),
                                      width: MySize.getScaledSizeWidth(20),
                                      decoration: BoxDecoration(
                                          gradient:
                                              const LinearGradient(colors: [
                                            ColorConstant.gradient1,
                                            ColorConstant.gradient2,
                                          ]),
                                          color: ColorConstant.primaryColor,
                                          shape: BoxShape.circle,
                                          border: Border.all(
                                              color: ColorConstant.white)),
                                    ),
                                  )
                                : const SizedBox.shrink(),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      resizeToAvoidBottomInset: false,
      floatingActionButton: Padding(
        padding: const EdgeInsets.only(bottom: 18.0),
        child: ValueListenableBuilder(
          valueListenable: hasData,
          builder: (context, value, child) => AppElevatedButton(
            width: MySize.getScaledSizeWidth(358),
            TypoGraphy.text("Submit", level: 3, color: ColorConstant.white),
            onPressed: () {
              context.read<ReminderSettingBloc>().add(UpdateReminderSetting(
                  upiId: upiIdController.text,
                  isReminderWithUPI: isWithLinkSelected.value ? true : false, isForUpdate: hasData.value));
            },
          ),
        ),
      ),
    );
  }
}
