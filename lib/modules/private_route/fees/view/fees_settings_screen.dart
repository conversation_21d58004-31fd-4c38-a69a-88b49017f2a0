import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:khelnet/common_widgets/custom_app_bar.dart';
import 'package:khelnet/common_widgets/custom_svg_picture.dart';
import 'package:khelnet/global/constants/size.dart';
import 'package:khelnet/utils/constants/app_asset.dart';
import 'package:khelnet/utils/constants/color_constant.dart';
import 'package:khelnet/utils/constants/routes_constant.dart';
import 'package:khelnet/utils/extension/navigation_extension.dart';
import 'package:khelnet/utils/theme/typography.dart';

class FeesSettingsScreen extends StatefulWidget {
  const FeesSettingsScreen({super.key});

  @override
  State<FeesSettingsScreen> createState() => _FeesSettingsScreenState();
}

class _FeesSettingsScreenState extends State<FeesSettingsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar("Fee Settings"),
      body: Center(
        child: Column(
          children: [
            Gap(MySize.getScaledSizeHeight(50)),
            ContainerHelper(
                onTap: () {
                  context.pushNamed(RouteConstant.gstSettingsScreen);
                },
                image: AppAsset.gstSettings,
                title: "GST Settings"),
            Gap(MySize.getScaledSizeHeight(18)),
            ContainerHelper(
                onTap: () {
                  context.pushNamed(RouteConstant.invoiceSettingScreen);
                },
                image: AppAsset.invoiceSettings,
                title: "Invoice Settings"),
            Gap(MySize.getScaledSizeHeight(18)),
            ContainerHelper(
                onTap: () {
                  context.pushNamed(RouteConstant.reminderSettingScreen);
                },
                image: AppAsset.notificationSetting,
                title: "Reminder Settings"),
          ],
        ),
      ),
    );
  }
}

class ContainerHelper extends StatelessWidget {
  final void Function() onTap;
  final String image, title;

  const ContainerHelper(
      {super.key,
      required this.onTap,
      required this.image,
      required this.title});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
            border: Border.all(color: ColorConstant.textFieldColor),
            color: ColorConstant.dashBoardTextField,
            borderRadius: const BorderRadius.all(Radius.circular(12))),
        height: MySize.getScaledSizeHeight(73),
        width: MySize.getScaledSizeWidth(363),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: Row(
            children: [
              CustomSvgPicture(image),
              Gap(MySize.getScaledSizeWidth(20)),
              TypoGraphy.text(title, level: 2),
              const Expanded(child: SizedBox.shrink()),
              Icon(
                Icons.arrow_forward_ios_rounded,
                size: MySize.getScaledSizeHeight(20),
              )
            ],
          ),
        ),
      ),
    );
  }
}
