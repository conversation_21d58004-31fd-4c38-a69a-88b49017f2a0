import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';
import 'package:khelnet/common_widgets/custom_svg_picture.dart';
import 'package:khelnet/common_widgets/failure_widget.dart';
import 'package:khelnet/common_widgets/loader_helper.dart';
import 'package:khelnet/modules/private_route/wallet/controller/bank_detail_bloc/bank_detail_bloc.dart';
import 'package:khelnet/modules/private_route/wallet/controller/bank_detail_bloc/bank_detail_event.dart';
import 'package:khelnet/modules/private_route/wallet/controller/bank_detail_bloc/bank_detail_state.dart';
import 'package:khelnet/modules/private_route/wallet/controller/cash_free_wallet_bloc/cash_free_wallet_event.dart';
import 'package:khelnet/modules/private_route/wallet/controller/cash_free_wallet_bloc/cash_free_wallet_state.dart';
import 'package:khelnet/modules/private_route/wallet/controller/waltte_overview_bloc/wallet_overview_bloc.dart';
import 'package:khelnet/modules/private_route/wallet/controller/waltte_overview_bloc/wallet_overview_event.dart';
import 'package:khelnet/modules/private_route/wallet/controller/waltte_overview_bloc/wallet_overview_state.dart';
import 'package:khelnet/modules/private_route/wallet/view/widget/holder_detail_widget.dart';
import 'package:khelnet/modules/private_route/wallet/view/widget/payment_receive_helper.dart';
import 'package:khelnet/utils/constants/color_constant.dart';
import 'package:khelnet/utils/theme/typography.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../common_widgets/buttons/app_elevated_button.dart';
import '../../../../common_widgets/custom_app_bar.dart';
import '../../../../common_widgets/dialog/loader/custom_dialog.dart';
import '../../../../global/constants/size.dart';
import '../../../../services/util_methods.dart';
import '../../../../utils/constants/app_asset.dart';
import '../../../../utils/constants/routes_constant.dart';
import '../controller/cash_free_wallet_bloc/cash_free_wallet_bloc.dart';

class WalletScreen extends StatefulWidget {
  const WalletScreen({super.key});

  @override
  State<WalletScreen> createState() => _WalletScreenState();
}

class _WalletScreenState extends State<WalletScreen> {
  final ScrollController _scrollController = ScrollController();
  final ValueNotifier<int> _page = ValueNotifier(1),
      totalReceivedAmount = ValueNotifier(0),
      todayReceivedAmount = ValueNotifier(0);
  ValueNotifier<bool> hasBankData = ValueNotifier(false);
  ValueNotifier<bool> isLoaderVisible = ValueNotifier(true);
  String startDate = DateFormat("dd MMM yyyy")
          .format(DateTime(DateTime.now().year, DateTime.now().month, 1)),
      endDate = DateFormat("dd MMM yyyy")
          .format(DateTime(DateTime.now().year, DateTime.now().month + 1, 0));

  ValueNotifier<DateTime> selectedDate = ValueNotifier(DateTime.now());
  ValueNotifier<bool> isCustomDateSelected = ValueNotifier(false);

  @override
  void initState() {
    super.initState();
    context.read<WalletOverviewBloc>().add(GetWalletOverviewData());
    context.read<BankDetailBloc>().add(GetBankDetails());
    loadHistory();
    _scrollController.addListener(
      () => UtilMethods().onScrollListener(() {
        _page.value++;
        loadHistory(shouldReload: false);
      }, _scrollController),
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _scrollController.removeListener(
      () => UtilMethods().onScrollListener(() {
        _page.value++;
        loadHistory(shouldReload: false);
      }, _scrollController),
    );
    super.dispose();
  }

  void loadHistory({bool shouldReload = true, bool isForSearch = false}) {
    context.read<CashFreeWalletBloc>().add(GetCashFreeData(
        startDate: startDate,
        endDate: endDate,
        page: _page.value,
        shouldReload: shouldReload,
        isForSearch: isForSearch));
  }

  ValueNotifier<String> accountHolderName = ValueNotifier(''),
      accountNumber = ValueNotifier(''),
      ifscCode = ValueNotifier('');

  bool shouldShowLeading() {
    final route = ModalRoute.of(context);
    if (route == null) return false;

    if (!Navigator.of(context).canPop()) return false;

    return route.settings.name != '/home';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        showLeading: shouldShowLeading(),
        "Payment Gateway Wallet",
      ),
      body: MultiBlocListener(
        listeners: [
          BlocListener<BankDetailBloc, BankDetailState>(
            listener: (context, state) {
              if (state is GetBankDetailSuccess) {
                isLoaderVisible.value = false;
                if (state.bankDetailModel.bankData != null) {
                  hasBankData.value = true;
                }
                accountHolderName.value =
                    state.bankDetailModel.bankData!.accountHolderName;
                ifscCode.value = state.bankDetailModel.bankData!.ifscCode;
                accountNumber.value =
                    state.bankDetailModel.bankData!.accountNumber;
              }
            },
          ),
          BlocListener<WalletOverviewBloc, WalletOverviewState>(
            listener: (context, state) {
              if (state is WalletOverviewSuccess) {
                todayReceivedAmount.value =
                    state.walletOverviewModel.data?.todayReceivedAmount ?? 0;
                totalReceivedAmount.value =
                    state.walletOverviewModel.data?.totalReceivedAmount ?? 0;
              }
            },
          )
        ],
        child: Center(
          child: ValueListenableBuilder(
            valueListenable: isLoaderVisible,
            builder: (context, value, child) => isLoaderVisible.value
                ? const LoaderHelper()
                : ValueListenableBuilder(
                    valueListenable: hasBankData,
                    builder: (context, value, child) => hasBankData.value
                        ? Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal: MySize.getScaledSizeWidth(20),
                                vertical: MySize.getScaledSizeHeight(30)),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                /*      Gap(MySize.getScaledSizeHeight(50)),
                                TypoGraphy.text("Payment Gateway Wallet",
                                    level: 3, fontWeight: FontWeight.w700),
                                Gap(MySize.getScaledSizeHeight(20)),*/
                                Container(
                                  width: MySize.getScaledSizeWidth(357),
                                  height: MySize.getScaledSizeHeight(148),
                                  decoration: const BoxDecoration(
                                    image: DecorationImage(
                                      image:
                                          AssetImage(AppAsset.walletContainer),
                                      fit: BoxFit.fill,
                                    ),
                                  ),
                                  child: Padding(
                                    padding: EdgeInsets.only(
                                      top: MySize.getScaledSizeHeight(20),
                                      left: MySize.getScaledSizeWidth(14),
                                    ),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        TypoGraphy.text("Total Money In Wallet",
                                            color: ColorConstant.white,
                                            fontSize:
                                                MySize.getScaledSizeHeight(12),
                                            fontWeight: FontWeight.w500),
                                        Row(
                                          children: [
                                            CustomSvgPicture(
                                              AppAsset.rupeeSvg,
                                              color: ColorConstant.white,
                                              height:
                                                  MySize.getScaledSizeHeight(
                                                      18),
                                            ),
                                            Gap(MySize.getScaledSizeWidth(3)),
                                            ValueListenableBuilder(
                                              valueListenable:
                                                  totalReceivedAmount,
                                              builder: (context, value,
                                                      child) =>
                                                  TypoGraphy.text(
                                                      totalReceivedAmount.value
                                                          .toString(),
                                                      color:
                                                          ColorConstant.white,
                                                      fontSize: MySize
                                                          .getScaledSizeHeight(
                                                              20),
                                                      fontWeight:
                                                          FontWeight.w500),
                                            ),
                                          ],
                                        ),
                                        Gap(MySize.getScaledSizeHeight(15)),
                                        Row(
                                          children: [
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                TypoGraphy.text("Today",
                                                    color: ColorConstant.white,
                                                    fontSize: MySize
                                                        .getScaledSizeHeight(
                                                            10),
                                                    fontWeight:
                                                        FontWeight.w500),
                                                Row(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  children: [
                                                    CustomSvgPicture(
                                                      AppAsset.rupeeSvg,
                                                      color:
                                                          ColorConstant.white,
                                                      height: MySize
                                                          .getScaledSizeHeight(
                                                              15),
                                                    ),
                                                    Gap(MySize
                                                        .getScaledSizeWidth(3)),
                                                    ValueListenableBuilder(
                                                      valueListenable:
                                                          todayReceivedAmount,
                                                      builder: (context, value,
                                                              child) =>
                                                          TypoGraphy.text(
                                                              todayReceivedAmount
                                                                  .value
                                                                  .toString(),
                                                              color:
                                                                  ColorConstant
                                                                      .white,
                                                              fontSize: MySize
                                                                  .getScaledSizeHeight(
                                                                      14),
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w500),
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ],
                                        )
                                      ],
                                    ),
                                  ),
                                ),
                                Gap(MySize.getScaledSizeHeight(15)),
                                SizedBox(
                                  height: MySize.getScaledSizeHeight(35),
                                  child: ListView(
                                    scrollDirection: Axis.horizontal,
                                    children: [
                                      HolderDetailWidget(
                                          title: "Name-",
                                          value: accountHolderName),
                                      Gap(MySize.getScaledSizeWidth(5)),
                                      HolderDetailWidget(
                                        title: "",
                                        value: accountNumber,
                                        isForUpi: true,
                                      ),
                                      Gap(MySize.getScaledSizeWidth(5)),
                                      HolderDetailWidget(
                                          title: "IFSC-", value: ifscCode),
                                    ],
                                  ),
                                ),
                                Gap(MySize.getScaledSizeHeight(17)),
                                // GestureDetector(
                                //   onTap: () {
                                //     context.pushNamed(
                                //         RouteConstant.statementStatusScreen);
                                //   },
                                //   child: Container(
                                //     decoration: BoxDecoration(
                                //         color: ColorConstant.dashBoardTextField,
                                //         borderRadius: const BorderRadius.all(
                                //             Radius.circular(10)),
                                //         border: Border.all(
                                //             color:
                                //                 ColorConstant.textFieldColor)),
                                //     height: MySize.getScaledSizeHeight(54),
                                //     width: MySize.getScaledSizeWidth(354),
                                //     child: Padding(
                                //       padding: const EdgeInsets.symmetric(
                                //           horizontal: 8),
                                //       child: Row(
                                //         children: [
                                //           const CustomSvgPicture(
                                //               AppAsset.statementStatus),
                                //           Gap(MySize.getScaledSizeWidth(15)),
                                //           Expanded(
                                //               child: TypoGraphy.text(
                                //                   "View Settlement Status",
                                //                   fontSize: MySize
                                //                       .getScaledSizeHeight(12),
                                //                   textAlign: TextAlign.start,
                                //                   fontWeight: FontWeight.w500)),
                                //           const GradientText(
                                //               gradient: LinearGradient(colors: [
                                //                 ColorConstant.white,
                                //                 ColorConstant.primaryColor,
                                //               ]),
                                //               child: Icon(
                                //                 CupertinoIcons.arrow_up_right,
                                //                 color:
                                //                     ColorConstant.primaryColor,
                                //               ))
                                //         ],
                                //       ),
                                //     ),
                                //   ),
                                // ),
                                // Gap(MySize.getScaledSizeHeight(14)),
                                Align(
                                  alignment: Alignment.centerLeft,
                                  child: TypoGraphy.text("Transactions History",
                                      level: 1, fontWeight: FontWeight.w500),
                                ),
                                Gap(MySize.getScaledSizeHeight(14)),
                                ValueListenableBuilder(
                                  valueListenable: isCustomDateSelected,
                                  builder: (context, value, child) => !value
                                      ? Row(
                                          children: [
                                            Container(
                                              height:
                                                  MySize.getScaledSizeHeight(
                                                      30),
                                              width: MySize.getScaledSizeWidth(
                                                  110),
                                              decoration: const BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.all(
                                                          Radius.circular(31)),
                                                  gradient:
                                                      LinearGradient(colors: [
                                                    ColorConstant.gradient1,
                                                    ColorConstant.gradient2,
                                                  ])),
                                              child: Center(
                                                child: TypoGraphy.text(
                                                    DateFormat('MMMM')
                                                        .format(DateTime.now()),
                                                    color: ColorConstant.white,
                                                    fontWeight: FontWeight.w500,
                                                    fontSize: MySize
                                                        .getScaledSizeHeight(
                                                            12)),
                                              ),
                                            ),
                                            Gap(MySize.getScaledSizeWidth(10)),
                                            GestureDetector(
                                                onTap: () {
                                                  CustomDialog.showCustomDatePicker(
                                                      context,
                                                      startDate: startDate != ''
                                                          ? DateFormat("dd MMM yyyy")
                                                              .parse(startDate)
                                                          : DateTime.now(),
                                                      endDate: endDate != ''
                                                          ? DateFormat(
                                                                  "dd MMM yyyy")
                                                              .parse(endDate)
                                                          : DateTime.now(),
                                                      onTap: (DateTime
                                                              selectedStartDate,
                                                          DateTime
                                                              selectedEndDate) {
                                                    isCustomDateSelected.value =
                                                        true;
                                                    setState(() {
                                                      startDate = DateFormat(
                                                              "dd MMM yyyy")
                                                          .format(
                                                              selectedStartDate);
                                                      endDate = DateFormat(
                                                              "dd MMM yyyy")
                                                          .format(
                                                              selectedEndDate);
                                                      loadHistory(
                                                          isForSearch: true);
                                                    });
                                                  });
                                                },
                                                child: const CustomSvgPicture(
                                                    AppAsset.customCalender))
                                          ],
                                        )
                                      : Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: [
                                            Container(
                                              height:
                                                  MySize.getScaledSizeHeight(
                                                      30),
                                              width: MySize.getScaledSizeWidth(
                                                  110),
                                              decoration: const BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.all(
                                                          Radius.circular(31)),
                                                  gradient:
                                                      LinearGradient(colors: [
                                                    ColorConstant.gradient1,
                                                    ColorConstant.gradient2,
                                                  ])),
                                              child: Center(
                                                  child: TypoGraphy.text(
                                                      startDate,
                                                      color:
                                                          ColorConstant.white,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      fontSize: MySize
                                                          .getScaledSizeHeight(
                                                              12))),
                                            ),
                                            Gap(MySize.getScaledSizeWidth(2)),
                                            Container(
                                              color: ColorConstant.gray,
                                              width:
                                                  MySize.getScaledSizeWidth(7),
                                              height:
                                                  MySize.getScaledSizeHeight(2),
                                            ),
                                            Gap(MySize.getScaledSizeWidth(2)),
                                            Container(
                                              height:
                                                  MySize.getScaledSizeHeight(
                                                      30),
                                              width: MySize.getScaledSizeWidth(
                                                  110),
                                              decoration: const BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.all(
                                                          Radius.circular(31)),
                                                  gradient:
                                                      LinearGradient(colors: [
                                                    ColorConstant.gradient1,
                                                    ColorConstant.gradient2,
                                                  ])),
                                              child: Center(
                                                  child: TypoGraphy.text(
                                                      endDate,
                                                      color:
                                                          ColorConstant.white,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      fontSize: MySize
                                                          .getScaledSizeHeight(
                                                              12))),
                                            ),
                                            Gap(MySize.getScaledSizeWidth(10)),
                                            CircleAvatar(
                                              radius: 15,
                                              backgroundColor: ColorConstant
                                                  .absentLightColor,
                                              child: GestureDetector(
                                                onTap: () {
                                                  isCustomDateSelected.value =
                                                      false;
                                                  setState(() {
                                                    startDate = DateFormat(
                                                            "dd MMM yyyy")
                                                        .format(DateTime(
                                                            DateTime.now().year,
                                                            DateTime.now()
                                                                .month,
                                                            1));
                                                    endDate = DateFormat(
                                                            "dd MMM yyyy")
                                                        .format(DateTime(
                                                            DateTime.now().year,
                                                            DateTime.now()
                                                                    .month +
                                                                1,
                                                            0));
                                                  });

                                                  loadHistory(
                                                      isForSearch: true);
                                                },
                                                child: CustomSvgPicture(
                                                  height: MySize
                                                      .getScaledSizeHeight(15),
                                                  AppAsset.crossIcon,
                                                  color: ColorConstant
                                                      .absentDarkColor,
                                                ),
                                              ),
                                            ),
                                            Gap(MySize.getScaledSizeWidth(10)),
                                            GestureDetector(
                                                onTap: () {
                                                  CustomDialog.showCustomDatePicker(
                                                      context,
                                                      startDate: startDate != ''
                                                          ? DateFormat("dd MMM yyyy")
                                                              .parse(startDate)
                                                          : DateTime.now(),
                                                      endDate: endDate != ''
                                                          ? DateFormat(
                                                                  "dd MMM yyyy")
                                                              .parse(endDate)
                                                          : DateTime.now(),
                                                      onTap: (DateTime
                                                              selectedStartDate,
                                                          DateTime
                                                              selectedEndDate) {
                                                    isCustomDateSelected.value =
                                                        true;
                                                    setState(() {
                                                      startDate = DateFormat(
                                                              "dd MMM yyyy")
                                                          .format(
                                                              selectedStartDate);
                                                      endDate = DateFormat(
                                                              "dd MMM yyyy")
                                                          .format(
                                                              selectedEndDate);
                                                      loadHistory(
                                                          isForSearch: true);
                                                    });
                                                  });
                                                },
                                                child: const CustomSvgPicture(
                                                    AppAsset.customCalender))
                                          ],
                                        ),
                                ),
                                Gap(MySize.getScaledSizeHeight(20)),
                                BlocBuilder<CashFreeWalletBloc,
                                    CashFreeWalletState>(
                                  builder: (BuildContext context,
                                      CashFreeWalletState state) {
                                    if (state is CashFreeSuccess) {
                                      return Expanded(
                                        child: state.cashFree.isNotEmpty
                                            ? ListView.separated(
                                                controller: _scrollController,
                                                padding: EdgeInsets.zero,
                                                itemBuilder: (context, index) {
                                                  return index >=
                                                              state.cashFree
                                                                  .length &&
                                                          !state.hasReachedMax
                                                      ? const LoaderHelper(
                                                          height: 30,
                                                        )
                                                      : PaymentReceiveHelper(
                                                          amount: state
                                                              .cashFree[index]
                                                              .order!
                                                              .orderAmount,
                                                          customerName: state
                                                              .cashFree[index]
                                                              .customerDetails!
                                                              .customerName,
                                                          customerPhone: state
                                                              .cashFree[index]
                                                              .customerDetails!
                                                              .customerPhone,
                                                          date: state
                                                              .cashFree[index]
                                                              .createdAt,
                                                          onTap: () {},
                                                          // onTap: () {
                                                          //   context.pushNamed(
                                                          //       RouteConstant
                                                          //           .transactionDetailScreen,
                                                          //       args: {
                                                          //         "amount": state
                                                          //             .cashFree[
                                                          //                 index]
                                                          //             .order
                                                          //             ?.orderAmount,
                                                          //         "name": state
                                                          //             .cashFree[
                                                          //                 index]
                                                          //             .customerDetails
                                                          //             ?.customerName,
                                                          //         "date": state
                                                          //             .cashFree[
                                                          //                 index]
                                                          //             .createdAt,
                                                          //         "number": state
                                                          //             .cashFree[
                                                          //                 index]
                                                          //             .customerDetails
                                                          //             ?.customerPhone,
                                                          //         "invoiceNumber": state
                                                          //             .cashFree[
                                                          //                 index]
                                                          //             .studentFeeTransaction
                                                          //             ?.invoiceNumber,
                                                          //         "upiId": state
                                                          //             .cashFree[
                                                          //                 index]
                                                          //             .payment
                                                          //             ?.paymentMethod
                                                          //             ?.upi
                                                          //             ?.upiId,
                                                          //         "transactionNumber": state
                                                          //             .cashFree[
                                                          //                 index]
                                                          //             .payment
                                                          //             ?.cfPaymentId,
                                                          //         "planName": state
                                                          //             .cashFree[
                                                          //                 index]
                                                          //             .studentFeeTransaction!
                                                          //             .studentData
                                                          //             .studentPlans[
                                                          //                 0]
                                                          //             .name,
                                                          //       });
                                                          // },
                                                        );
                                                },
                                                separatorBuilder: (context,
                                                        index) =>
                                                    Gap(MySize
                                                        .getScaledSizeHeight(
                                                            14)),
                                                itemCount: state.hasReachedMax
                                                    ? state.cashFree.length
                                                    : state.cashFree.length + 1)
                                            : const Center(
                                                child: CustomSvgPicture(AppAsset
                                                    .noPaymentTransaction),
                                              ),
                                      );
                                    } else if (state is CashFreeFailure) {
                                      return FailureWidget(msg: state.msg);
                                    }
                                    return const LoaderHelper();
                                  },
                                )
                              ],
                            ),
                          )
                        : SingleChildScrollView(
                            child: Column(
                              children: [
                                Gap(MySize.getScaledSizeHeight(70)),
                                const CustomSvgPicture(AppAsset.appNameLogo),
                                Gap(MySize.getScaledSizeHeight(40)),
                                Container(
                                  height: MySize.getScaledSizeHeight(434),
                                  width: MySize.getScaledSizeWidth(354),
                                  decoration: const BoxDecoration(
                                    image: DecorationImage(
                                        image: AssetImage(
                                            AppAsset.walletContainer),
                                        fit: BoxFit.fill),
                                  ),
                                  child: Column(
                                    children: [
                                      Gap(MySize.getScaledSizeHeight(34)),
                                    ],
                                  ),
                                ),
                                Gap(MySize.getScaledSizeHeight(26)),
                                SizedBox(
                                  width: MySize.getScaledSizeWidth(350),
                                  child: TypoGraphy.text(
                                      "Please submit a request to enable the payment gateway feature. Our team will get in touch with you to initiate the process.",
                                      level: 1,
                                      textAlign: TextAlign.start,
                                      fontWeight: FontWeight.w500,
                                      color: ColorConstant.gray),
                                ),
                                Gap(MySize.getScaledSizeHeight(20)),
                                Padding(
                                  padding: EdgeInsets.only(
                                      bottom: MySize.getScaledSizeHeight(10)),
                                  child: AppElevatedButton(
                                    height: MySize.getScaledSizeHeight(57),
                                    width: MySize.getScaledSizeWidth(358),
                                    TypoGraphy.text("Contact Us",
                                        level: 3, color: ColorConstant.white),
                                    onPressed: () async {
                                      final Uri url = Uri(
                                          scheme: 'tel', path: "9891787971");
                                      if (await canLaunchUrl(url)) {
                                        await launchUrl(url);
                                      } else {
                                        log("Can't open");
                                      }
                                    },
                                  ),
                                )
                              ],
                            ),
                          ),
                  ),
          ),
        ),
      ),
    );
  }
}
