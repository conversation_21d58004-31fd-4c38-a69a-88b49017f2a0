import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:intl/intl.dart';

import '../../../../../common_widgets/custom_svg_picture.dart';
import '../../../../../global/constants/size.dart';
import '../../../../../utils/constants/app_asset.dart';
import '../../../../../utils/constants/color_constant.dart';
import '../../../../../utils/theme/typography.dart';

class PaymentReceiveHelper extends StatelessWidget {
  final int amount;
  final String customerName, customerPhone;
  final DateTime date;
  final void Function() onTap;

  const PaymentReceiveHelper(
      {super.key,
      required this.amount,
      required this.onTap,
      required this.customerName,
      required this.customerPhone,
      required this.date});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: MySize.getScaledSizeHeight(70),
        width: MySize.getScaledSizeWidth(354),
        decoration: BoxDecoration(
            color: ColorConstant.dashBoardTextField,
            border: Border.all(color: ColorConstant.textFieldColor),
            borderRadius: const BorderRadius.all(Radius.circular(8))),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TypoGraphy.text("Fee received from $customerName",
                  fontSize: MySize.getScaledSizeHeight(12),
                  fontWeight: FontWeight.w500),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TypoGraphy.text(customerPhone,
                      fontSize: MySize.getScaledSizeHeight(12),
                      color: ColorConstant.gray,
                      fontWeight: FontWeight.w500),
                  const Expanded(child: SizedBox.shrink()),
                  CustomSvgPicture(
                    AppAsset.rupeeSvg,
                    height: MySize.getScaledSizeHeight(11),
                  ),
                  Gap(MySize.getScaledSizeWidth(3)),
                  TypoGraphy.text(amount.toString(),
                      fontSize: MySize.getScaledSizeHeight(14),
                      fontWeight: FontWeight.w500),
                ],
              ),
              TypoGraphy.text(DateFormat("dd MMM yyyy").format(date.toLocal()),
                  fontSize: MySize.getScaledSizeHeight(10),
                  color: ColorConstant.gray,
                  fontWeight: FontWeight.w500),
            ],
          ),
        ),
      ),
    );
  }
}
