import 'package:flutter/material.dart';
import 'package:khelnet/common_widgets/custom_app_bar.dart';
import 'package:khelnet/common_widgets/custom_svg_picture.dart';
import 'package:khelnet/utils/constants/app_asset.dart';

class ComingSoonScreen extends StatefulWidget {
  const ComingSoonScreen({super.key});

  @override
  State<ComingSoonScreen> createState() => _ComingSoonScreenState();
}

class _ComingSoonScreenState extends State<ComingSoonScreen> {
  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      appBar: CustomAppBar("Coming soon",showLeading: false,),
      body: CustomSvgPicture(AppAsset.comingSoon),
    );
  }
}
